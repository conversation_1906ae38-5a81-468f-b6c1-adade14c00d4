import {DataController} from '../../../base/baseController';
import {
  groupMemberRoleStatus,
  groupRole,
  StorageContanst,
} from '../../../Config/Contanst';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {randomGID} from '../../../utils/Utils';
import {CustomerDA} from '../../customer/da';
export class GroupDA {
  private groupController: DataController;
  private groupMemberController: DataController;
  private postController: DataController;

  constructor() {
    this.groupController = new DataController('Group');
    this.groupMemberController = new DataController('Role');
    this.postController = new DataController('Posts');
  }

  async getList(page?: number, size?: number, query?: string) {
    const response = await this.groupController.getListSimple({
      page: page ?? 1,
      size: size ?? 10,
      query: query ?? '*',
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getById(id: string) {
    const response = await this.groupController.getById(id);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async add(group: any) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const newGroup = {
        ...group,
        CustomerId: cusId,
        DateCreated: new Date().getTime(),
      };

      const response = await this.groupController.add([newGroup]);
      if (response?.code === 200) {
        // Tự động thêm người tạo vào nhóm như admin
        await this.groupMemberController.add([
          {
            Id: randomGID(),
            GroupId: newGroup.Id,
            CustomerId: cusId,
            Type: groupRole.admin,
            DateCreated: new Date().getTime(),
            Status: groupMemberRoleStatus.joined,
          },
        ]);
        return newGroup;
      }
    }
    return null;
  }

  async edit(group: any) {
    if (!group.Id) {
      return null;
    }
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const currentGroup = await this.getById(group.Id);
      if (currentGroup?.data?.CustomerId === cusId) {
        const response = await this.groupController.edit([group]);

        if (response?.code === 200) {
          return response;
        }
      }
    }
    return null;
  }

  async delete(id: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const currentGroup = await this.getById(id);
      if (currentGroup?.data?.CustomerId === cusId) {
        const response = await this.groupController.delete([id]);
        if (response?.code === 200) {
          // Xóa tất cả thành viên của nhóm
          const members = await this.groupMemberController.getListSimple({
            query: `@GroupId:{${id}}`,
          });
          if (members?.code === 200 && members.data?.length > 0) {
            await this.groupMemberController.delete(
              members.data.map((m: any) => m.Id),
            );
          }
          return true;
        }
      }
    }
    return false;
  }

  async getMyGroups(page?: number, size?: number) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const response = await this.groupController.getListSimple({
        page: page ?? 1,
        size: size ?? 10,
        query: `@CustomerId:{${cusId}}`,
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      });
      if (response?.code === 200) {
        return response;
      }
    }
    return null;
  }
  //lấy danh sách group đã joined
  async getJoinedGroups(page?: number, size?: number) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      // Lấy danh sách GroupId mà user đã tham gia
      const memberResponse = await this.groupMemberController.getListSimple({
        query: `@CustomerId:{${cusId}} @Status:[${groupMemberRoleStatus.joined}]`,
      });
      if (memberResponse?.code === 200 && memberResponse.data?.length > 0) {
        const groupIds = memberResponse.data.map((m: any) => m.GroupId);
        const response = await this.groupController.getListSimple({
          page: page ?? 1,
          size: size ?? 10,
          query: `@Id:{${groupIds.join(' | ')}}`,
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });
        if (response?.code === 200) {
          return response;
        }
      }
    }
    return null;
  }

  async getMemberRole(
    groupId: string,
    memberId: string,
  ): Promise<number | null> {
    const member = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${memberId}} @Status:[${groupMemberRoleStatus.joined}]`,
    });
    if (member?.code === 200 && member.data?.length > 0) {
      return member.data[0].Type;
    }
    return null;
  }

  async updateMemberRole(groupId: string, memberId: string, newRole: number) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (!cusId) {
      return null;
    }

    // Kiểm tra quyền của người thực hiện thao tác (phải là admin)
    const currentUserRole = await this.getMemberRole(groupId, cusId);
    if (currentUserRole !== groupRole.admin) {
      return null;
    }

    // Kiểm tra member cần update có tồn tại không
    const memberResponse = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${memberId}} @Status:[${groupMemberRoleStatus.joined}]`,
    });

    if (memberResponse?.code === 200 && memberResponse.data?.length > 0) {
      const memberData = memberResponse.data[0];

      // Không cho phép thay đổi role của chính mình
      if (memberId === cusId) {
        return null;
      }

      // Cập nhật role mới
      const updateData = {
        ...memberData,
        Type: newRole,
      };

      const response = await this.groupMemberController.edit([updateData]);
      if (response?.code === 200) {
        if (newRole === groupRole.admin) {
          var group = await this.groupController.getById(groupId);
          await this.groupController.edit([
            {
              ...group.data,
              CustomerId: memberId,
            },
          ]);
          const memberAdmin = await this.groupMemberController.getListSimple({
            query: `@GroupId:{${groupId}} @CustomerId:{${cusId}}`,
          });
          await this.groupMemberController.edit([
            {
              ...memberAdmin.data[0],
              Type: groupRole.subadmin,
              Status: groupMemberRoleStatus.joined,
            },
          ]);
        }
        return updateData;
      }
    }
    return null;
  }

  async getGroupMembers(
    groupId: string,
    page?: number,
    size?: number,
    role?: number,
  ) {
    let query = `@GroupId:{${groupId}} @Status: [${groupMemberRoleStatus.joined}]`;
    if (role) {
      query += `| @Type:{${role}}`;
    }
    const response = await this.groupMemberController.getPatternList({
      page: page ?? 1,
      size: size ?? 10,
      query: query,
      pattern: {
        CustomerId: ['Id', 'Name', 'AvatarUrl'],
      },
    });
    if (response?.code === 200) {
      const customersResult = response.Customer;
      if (customersResult?.length > 0) {
        const customers = customersResult.map((item: any) => {
          return {
            ...item,
            DateCreated: response.data.find(
              (m: any) => m.CustomerId === item.Id,
            )?.DateCreated,
            Role: response.data.find((m: any) => m.CustomerId === item.Id)
              ?.Type,
          };
        });
        return customers;
      }
    }
    return null;
  }

  async getRoles(groupId: string, customerId: string) {
    const response = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${customerId}}`,
    });
    if (response?.code === 200 && response.data?.length > 0) {
      return response.data[0].Type;
    }
    return null;
  }
  async deleteRoles(groupId: string, customerId: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (!cusId) {
      return false;
    }
    // Kiểm tra xem user có phải là thành viên của group không
    const memberRole = await this.getMemberRole(groupId, cusId);
    if (!memberRole) {
      return false;
    }
    // Kiểm tra xem user có quyền xóa thành viên không (admin hoặc subadmin)
    if (memberRole !== groupRole.admin && memberRole !== groupRole.subadmin) {
      return false;
    }
    // Xóa thành viên
    const response = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${customerId}} @Status:[${groupMemberRoleStatus.joined}]`,
    });
    if (response?.code === 200 && response.data?.length > 0) {
      const deleteResult = await this.groupMemberController.delete([
        response.data[0].Id,
      ]);
      if (deleteResult.code === 200) {
        return true;
      }
    }
    return false;
  }
  async addPost(post: any) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (!cusId) {
      return null;
    }

    // Kiểm tra xem user có phải là thành viên của group không
    const memberRole = await this.getMemberRole(post.GroupId!, cusId);
    if (!memberRole) {
      return null;
    }

    const newPost = {
      ...post,
      Id: randomGID(),
      CustomerId: cusId,
      DateCreated: new Date().getTime(),
    };

    const response = await this.postController.add([newPost]);
    if (response?.code === 200) {
      return newPost;
    }
    return null;
  }

  async editPost(post: any) {
    if (!post.Id) {
      return null;
    }

    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (!cusId) {
      return null;
    }

    // Kiểm tra xem user có phải là người tạo post không
    const currentPost = await this.postController.getById(post.Id);
    if (currentPost?.data?.CustomerId !== cusId) {
      return null;
    }
    const response = await this.postController.edit([post]);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async deletePost(postId: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (!cusId) {
      return false;
    }

    // Lấy thông tin post
    const post = await this.postController.getById(postId);
    if (!post?.data) {
      return false;
    }

    // Kiểm tra quyền xóa (người tạo post hoặc admin của group)
    const isCreator = post.data.CustomerId === cusId;
    const memberRole = await this.getMemberRole(post.data.GroupId, cusId);
    const canDelete =
      isCreator ||
      memberRole === groupRole.admin ||
      memberRole === groupRole.subadmin;

    if (canDelete) {
      const response = await this.postController.delete([postId]);
      if (response?.code === 200) {
        return true;
      }
    }
    return false;
  }

  async isFollowingGroup(groupId: string): Promise<boolean> {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (!cusId) return false;

    const memberResult = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${cusId}} @Status:[${groupMemberRoleStatus.joined}]`,
      size: 1,
      returns: ['Id'],
    });

    return memberResult?.code === 200 && memberResult.data?.length > 0;
  }
  async addMemberInvite(groupId: string, memberId: string) {
    const data = {
      Id: randomGID(),
      GroupId: groupId,
      CustomerId: memberId,
      Type: groupRole.member,
      DateCreated: new Date().getTime(),
      Status: groupMemberRoleStatus.invited, // đang chờ
    };
    const response = await this.groupMemberController.add([data]);
    if (response?.code === 200) {
      return true;
    }
    return false;
  }
  async checkExistsMemberInGroup(groupId: string, memberId: string) {
    const response = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${memberId}} @Status:[${groupMemberRoleStatus.joined}]`,
      size: 1,
      returns: ['Id', 'Status'],
    });
    if (response?.code === 200 && response.data?.length > 0) {
      return response.data[0];
    }
    return null;
  }
  async acceptMemberInvite(groupId: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const response = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${cusId}} @Status:[${groupMemberRoleStatus.invited}]`,
      size: 1,
      returns: ['Id'],
    });

    if (response?.code === 200 && response.data?.length > 0) {
      const data = response.data[0];
      data.Status = groupMemberRoleStatus.joined; // xác nhận
      const result = await this.groupMemberController.edit([data]);
      if (result?.code === 200) {
        return true;
      }
    }
    return false;
  }
  async rejectMemberInvite(groupId: string) {
    // xóa luôn member đó trong nhóm
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const response = await this.groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${cusId}} @Status:[${groupMemberRoleStatus.invited}]`,
      size: 1,
      returns: ['Id'],
    });
    if (response?.code === 200 && response.data?.length > 0) {
      const data = response.data[0];
      const result = await this.groupMemberController.delete([data.Id]);
      if (result?.code === 200) {
        return true;
      }
    }
    return false;
  }
  //lấy danh sách group đang cần confirm
  async getGroupConfirm() {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const response = await this.groupMemberController.getListSimple({
      query: `@Status:[${groupMemberRoleStatus.invited}] @CustomerId:{${cusId}}`,
      size: 10,
      returns: ['GroupId'],
    });
    if (response?.code === 200 && response.data?.length > 0) {
      const groupIds = response.data.map((item: any) => item.GroupId);
      // lấy danh sách group theo danh sách groupIds
      const groupResponse = await this.groupController.getListSimple({
        query: `@Id:{${groupIds.join(' | ')}}`,
        size: 10,
        returns: ['Id', 'Name', 'Thumb', 'Description', 'CustomerId'],
      });
      if (groupResponse?.code === 200) {
        return groupResponse;
      }
    }
    return null;
  }
}

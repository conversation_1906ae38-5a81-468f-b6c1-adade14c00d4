import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {useEffect} from 'react';
import {newsFeedActions} from '../reducers/newsFeedReducer';

export function useNewsFeedData(page: number, size: number = 10) {
  const dispatch: AppDispatch = useDispatch();
  const data = useSelector((state: RootState) => state.newsFeed.data);
  const loading = useSelector((state: RootState) => state.newsFeed.loading);
  const error = useSelector((state: RootState) => state.newsFeed.error);
  const reduxPage = useSelector((state: RootState) => state.newsFeed.page);

  useEffect(() => {
    // Chỉ gọi API khi component mount lần đầu
    if (loading === true) {
      console.log(111);
      dispatch(newsFeedActions.getNewFeed(page, size ?? 10));
    }
  }, [dispatch, page]); // Update dependencies to include page and size

  return {data, loading, error, page: reduxPage};
}

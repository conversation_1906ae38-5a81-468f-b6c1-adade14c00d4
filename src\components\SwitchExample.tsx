import React, {useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import IOSSwitch from './IOSSwitch';
import {ColorThemes} from '../assets/skin/colors';
import {TypoSkin} from '../assets/skin/typography';

const SwitchExample = () => {
  const [isEnabled1, setIsEnabled1] = useState(false);
  const [isEnabled2, setIsEnabled2] = useState(true);
  const [isEnabled3, setIsEnabled3] = useState(false);
  const [isEnabled4, setIsEnabled4] = useState(true);

  return (
    <View style={styles.container}>
      <Text style={[TypoSkin.heading4, styles.title]}>
        iOS Style Switch Examples
      </Text>

      {/* Default switch */}
      <View style={styles.row}>
        <Text style={TypoSkin.regular3}>Default</Text>
        <IOSSwitch value={isEnabled1} onValueChange={setIsEnabled1} />
      </View>

      {/* Custom color switch */}
      <View style={styles.row}>
        <Text style={TypoSkin.regular3}>Custom Color</Text>
        <IOSSwitch
          value={isEnabled2}
          onValueChange={setIsEnabled2}
          onColor={ColorThemes.light.Primary_Color_Main}
        />
      </View>

      {/* Larger size switch */}
      <View style={styles.row}>
        <Text style={TypoSkin.regular3}>Larger Size</Text>
        <IOSSwitch
          value={isEnabled3}
          onValueChange={setIsEnabled3}
          size={1.2}
          onColor={ColorThemes.light.Secondary_1_Color_Main}
        />
      </View>

      {/* Disabled switch */}
      <View style={styles.row}>
        <Text style={TypoSkin.regular3}>Disabled</Text>
        <IOSSwitch
          value={isEnabled4}
          onValueChange={setIsEnabled4}
          disabled={true}
        />
      </View>

      {/* Status display */}
      <View style={styles.statusContainer}>
        <Text style={TypoSkin.regular2}>
          Switch 1:{' '}
          <Text style={styles.statusValue}>{isEnabled1 ? 'ON' : 'OFF'}</Text>
        </Text>
        <Text style={TypoSkin.regular2}>
          Switch 2:{' '}
          <Text style={styles.statusValue}>{isEnabled2 ? 'ON' : 'OFF'}</Text>
        </Text>
        <Text style={TypoSkin.regular2}>
          Switch 3:{' '}
          <Text style={styles.statusValue}>{isEnabled3 ? 'ON' : 'OFF'}</Text>
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#fff',
  },
  title: {
    marginBottom: 20,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  statusContainer: {
    marginTop: 30,
    padding: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  statusValue: {
    fontWeight: 'bold',
  },
});

export default SwitchExample;

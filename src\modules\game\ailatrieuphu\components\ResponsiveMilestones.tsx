import React, { useState } from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import { useResponsivePositions, useOptimalPositionStrategy, PositionStrategy } from '../hooks/useResponsivePositions';

interface ResponsiveMilestonesProps {
  containerDimensions: { width: number; height: number };
  milestones: Array<{
    id: number;
    status: 'completed' | 'in-progress' | 'locked';
    levelName?: string;
  }>;
  onMilestonePress: (id: number) => void;
}

const ResponsiveMilestones: React.FC<ResponsiveMilestonesProps> = ({
  containerDimensions,
  milestones,
  onMilestonePress,
}) => {
  // Auto-detect strategy tốt nhất hoặc cho phép user chọn
  const optimalStrategy = useOptimalPositionStrategy();
  const [currentStrategy, setCurrentStrategy] = useState<PositionStrategy>(optimalStrategy);
  
  const { positions } = useResponsivePositions(currentStrategy, containerDimensions);

  const strategies: PositionStrategy[] = ['responsive', 'aspectRatio', 'grid', 'percentage'];

  return (
    <View style={[styles.container, { width: containerDimensions.width, height: containerDimensions.height }]}>
      {/* Debug panel - có thể ẩn trong production */}
      <View style={styles.debugPanel}>
        <Text style={styles.debugText}>Strategy: {currentStrategy}</Text>
        <View style={styles.strategyButtons}>
          {strategies.map(strategy => (
            <TouchableOpacity
              key={strategy}
              style={[
                styles.strategyButton,
                currentStrategy === strategy && styles.activeStrategy
              ]}
              onPress={() => setCurrentStrategy(strategy)}
            >
              <Text style={[
                styles.strategyButtonText,
                currentStrategy === strategy && styles.activeStrategyText
              ]}>
                {strategy}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Render milestones */}
      {positions.map((position, index) => {
        const milestone = milestones.find(m => m.id === position.id);
        if (!milestone) return null;

        const pixelX = position.left * containerDimensions.width;
        const pixelY = position.top * containerDimensions.height;

        return (
          <TouchableOpacity
            key={position.id}
            style={[
              styles.milestone,
              {
                left: pixelX - 25, // Offset để center milestone
                top: pixelY - 25,
              },
              milestone.status === 'completed' && styles.completedMilestone,
              milestone.status === 'in-progress' && styles.inProgressMilestone,
              milestone.status === 'locked' && styles.lockedMilestone,
            ]}
            onPress={() => onMilestonePress(milestone.id)}
          >
            <Text style={styles.milestoneNumber}>{milestone.id}</Text>
            {milestone.levelName && (
              <Text style={styles.levelName}>{milestone.levelName}</Text>
            )}
            
            {/* Debug info */}
            <Text style={styles.debugPosition}>
              {`${(position.left * 100).toFixed(1)}%, ${(position.top * 100).toFixed(1)}%`}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    backgroundColor: 'rgba(0,0,0,0.1)', // Debug background
  },
  debugPanel: {
    position: 'absolute',
    top: 10,
    left: 10,
    right: 10,
    backgroundColor: 'rgba(255,255,255,0.9)',
    padding: 10,
    borderRadius: 5,
    zIndex: 1000,
  },
  debugText: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  strategyButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  strategyButton: {
    backgroundColor: '#ddd',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 3,
    marginRight: 5,
    marginBottom: 5,
  },
  activeStrategy: {
    backgroundColor: '#007AFF',
  },
  strategyButtonText: {
    fontSize: 10,
    color: '#333',
  },
  activeStrategyText: {
    color: '#fff',
  },
  milestone: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  completedMilestone: {
    backgroundColor: '#4CAF50',
    borderColor: '#2E7D32',
  },
  inProgressMilestone: {
    backgroundColor: '#FF9800',
    borderColor: '#F57C00',
  },
  lockedMilestone: {
    backgroundColor: '#9E9E9E',
    borderColor: '#616161',
  },
  milestoneNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  levelName: {
    position: 'absolute',
    bottom: -20,
    fontSize: 8,
    color: '#333',
    textAlign: 'center',
    width: 60,
    left: -5,
  },
  debugPosition: {
    position: 'absolute',
    top: -15,
    fontSize: 8,
    color: '#666',
    textAlign: 'center',
    width: 60,
    left: -5,
  },
});

export default ResponsiveMilestones;

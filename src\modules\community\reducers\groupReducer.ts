import { createSlice, PayloadAction, Dispatch } from '@reduxjs/toolkit';
import { GroupDA } from '../groups/da';
import { StorageContanst } from '../../../Config/Contanst';
import { getDataToAsyncStorage } from '../../../utils/AsyncStorage';

interface GroupWithMemberCount {
  Id: string;
  Name: string;
  ImageUrl: string;
  Description: string;
  MemberCount: number;
}

interface GroupState {
  groups: GroupWithMemberCount[];
  isLoading: boolean;
  error: string | null;
  page: number;
  hasMore: boolean;
  searchTerm: string;
}

const initialState: GroupState = {
  groups: [],
  isLoading: false,
  error: null,
  page: 1,
  hasMore: true,
  searchTerm: ''
};

export const groupSlice = createSlice({
  name: 'groups',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setGroups: (state, action: PayloadAction<any>) => {
      state.groups = action.payload.groups;
      state.page = action.payload.page;
      state.hasMore = action.payload.groups.length > 0;
    },
    addGroups: (state, action: PayloadAction<any>) => {
      state.groups = [action.payload.groups, ...state.groups];
    },
    updateMemberCount: (state, action: PayloadAction<{groupId: string; change: number}>) => {
      state.groups = state.groups.map(group =>
        group.Id === action.payload.groupId
          ? {...group, MemberCount: group.MemberCount + action.payload.change}
          : group
      );
    },
    appendGroups: (state, action: PayloadAction<{groups: GroupWithMemberCount[]; page: number}>) => {
      state.groups = [...state.groups, ...action.payload.groups];
      state.page = action.payload.page;
      state.hasMore = action.payload.groups.length > 0;
    },
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.searchTerm = action.payload;
      state.page = 1; // Reset page when search term changes
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    resetState: () => initialState
  }
});

export default groupSlice.reducer;

const {
  setLoading,
  setGroups,
  addGroups,
  updateMemberCount,
  appendGroups,
  setSearchTerm,
  setError,
  resetState
} = groupSlice.actions;

const groupDA = new GroupDA();

export class GroupActions {
  static getAllGroups = (page: number = 1, size: number = 10, search: string = '') => 
    async (dispatch: Dispatch) => {
      try {
        dispatch(setLoading(true));
        const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
        // lấy tất cả group mà user đấy chưa tham gia
        var query = '';
        if(cusId) {
          const memberResult = await groupDA.getJoinedGroups();
          if (memberResult?.code === 200 && memberResult.data?.length > 0) {
            const groupIds = memberResult.data.map((m: any) => m.Id);
            query += ` -@Id:{${groupIds.join(' | ')}}`;
          }
        }
        if(search.length) {query = `@Name:${search}*`;}

        const result = await groupDA.getList(page, size, query);
        if (result) {
          // Lấy member count cho mỗi group
          const groupsWithMemberCount = await Promise.all(
            result.data.map(async (group: any) => {
              const memberResult = await groupDA.getGroupMembers(group.Id);
              return {
                ...group,
                MemberCount: memberResult ? memberResult.length : 0,
                MemberList: memberResult,
              };
            })
          );
          dispatch(
            page === 1
              ? setGroups({ groups: groupsWithMemberCount, page })
              : appendGroups({ groups: groupsWithMemberCount, page })
          );
        } else {
          dispatch(setError('Failed to fetch groups'));
          dispatch(page === 1 ? setGroups({ groups: [], page }) : appendGroups({ groups: [], page }));
        }
      } catch (error) {
        dispatch(setError(error instanceof Error ? error.message : 'An error occurred'));
      } finally {
        dispatch(setLoading(false));
      }
    };

  static searchGroups = (searchTerm: string) => async (dispatch: Dispatch) => {
    dispatch(setSearchTerm(searchTerm));
    await GroupActions.getAllGroups(1, 10, searchTerm);
  };

  static getGroupById = (groupId: string) => async (dispatch: Dispatch) => {
    try {
      dispatch(setLoading(true));
      const result = await groupDA.getById(groupId);
      
      if (result) {
        const memberResult = await groupDA.getGroupMembers(groupId);
        const groupWithMemberCount = {
          ...result.data,
          MemberCount: memberResult ? memberResult.length : 0,
          MemberList: memberResult,
        };
        
        dispatch(setGroups({ groups: [groupWithMemberCount], page: 1 }));
      } else {
        dispatch(setError('Failed to fetch group'));
      }
    } catch (error) {
      dispatch(setError(error instanceof Error ? error.message : 'An error occurred'));
    } finally {
      dispatch(setLoading(false));
    }
  };

  static resetGroups = () => (dispatch: Dispatch) => {
    dispatch(resetState());
  };
  static updateMemberCount = (groupId: string, change: number) => (dispatch: Dispatch) => {
    dispatch(updateMemberCount({ groupId, change }));
  };
  static addGroup = (groupData: {
    Name: string;
    Description?: string;
    Thumb?: string;
  }) => async (dispatch: Dispatch) => {
    try {
      dispatch(setLoading(true));
      
      const result = await groupDA.add(groupData);
      if (result) {
        // Thêm thành công, lấy thông tin group mới với member count
        const memberResult = await groupDA.getGroupMembers(result.Id);
        const newGroup = {
          ...result,
          MemberCount: memberResult ? memberResult.length : 1, // 1 vì người tạo tự động là thành viên
          MemberList: memberResult,
        };
        
        // Thêm group mới vào đầu danh sách
        dispatch(addGroups({
          groups: newGroup,
        }));

        return true;
      } else {
        dispatch(setError('Failed to create group'));
        return false;
      }
    } catch (error) {
      dispatch(setError(error instanceof Error ? error.message : 'An error occurred'));
      return false;
    } finally {
      dispatch(setLoading(false));
    }
  };
}




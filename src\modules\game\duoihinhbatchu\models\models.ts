interface Question {
  id: string;
  title: string;
  image: string;
  hint: string;
  answer: string;
  level: number;
  description: string;
}

interface DHBCGameConfig {
  gameId: string;
  scorePerLife: number;
  maxLives: number;
  timeLimit: number;
  bonusScore: number;
  isActive: boolean;
  gemHint: number;
  score: number;
}

// Interface cho GameQuestion API response
interface DHBCGameQuestionAPI {
  Id: string;
  GameId: string;
  Stage: number;
  Name: string; // Text câu hỏi
  Img: string; // URL hình ảnh
  Purpose: string; // CompetenceId
  Sort?: number;
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
  Suggest: string;
}

// Interface cho GameAnswer API response
interface DHBCGameAnswerAPI {
  Id: string;
  GameQuestionId: string;
  Name: string; // Đáp án đúng
  IsCorrect: boolean; // Để hiển thị đúng/sai
  Sort?: number;
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
}

// Interface cho GameConfig API response
interface DHBCGameConfigAPI {
  Id: string;
  GameId: string;
  Score: number; // Điểm trên mỗi mạng
  LifeCount: number; // Số mạng chơi
  Time: number; // Thời gian chơi (giây)
  Bonus: number; // Điểm bonus khi hoàn thành không mất mạng
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
  ScoreHint: number;
}

// Interface cho question đã transform để sử dụng trong game
interface DHBCQuestion {
  id: string;
  text: string;
  image: string;
  hint: string;
  answer: string;
}

// Interface cho game config đã transfo

export type {
  Question,
  DHBCGameQuestionAPI,
  DHBCGameAnswerAPI,
  DHBCGameConfigAPI,
  DHBCQuestion,
  DHBCGameConfig,
};

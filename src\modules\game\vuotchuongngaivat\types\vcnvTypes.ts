// TypeScript interfaces cho VCNV Game

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T[];
  total?: number;
}

// Data từ bảng GameConfig
export interface VCNVGameConfigAPI {
  Id: string;
  GameId: string;
  Score: number;        // Điểm trên mỗi mạng
  LifeCount: number;    // Số mạng chơi
  Time: number;         // Thời gian chơi (giây)
  Bonus: number;        // Điểm bonus khi hoàn thành không mất mạng
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
  ScoreHint: number;
}

// Data từ bảng GameQuestion
export interface VCNVGameQuestionAPI {
  Id: string;
  GameId: string;
  Stage: number;
  Name: string;         // Nội dung câu hỏi
  Sort: number;         // 1-8: hàng ngang, 9: từ khóa dọc
  Purpose: string;      // CompetenceId
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
  Suggest: string;
  Audio?: string;       // URL file audio (optional)
}

// Data từ bảng GameAnswer
export interface VCNVGameAnswerAPI {
  Id: string;
  GameQuestionId: string;
  Name: string;         // Câu trả lời (sẽ split thành từ)
  Sort: number;     // Vị trí key trong câu hỏi đó (bắt đầu từ 1)
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
}

// Interface cho Japanese split API response
export interface JapaneseSplitToken {
  surface: string;
  basic_form: string;
  pos: string;
  pos_detail_1: string;
  reading: string;
  pronunciation: string;
}

export interface JapaneseSplitResponse {
  tokens: JapaneseSplitToken[];
}

// Transformed interfaces for game use
export interface VCNVGameConfig {
  gameId: string;
  scorePerLife: number;
  maxLives: number;
  timeLimit: number;
  bonusScore: number;
  isActive: boolean;
  gemHint: number;
}

export interface VCNVWord {
  id: string;
  text: string;
  correctPosition: number;  // Vị trí đúng trong câu (1, 2, 3...)
  isKey: boolean;          // Có phải từ key không
}

export interface VCNVQuestion {
  id: string;
  questionText: string;
  sort: number;           // 1-8: hàng ngang, 9: từ khóa dọc
  words: VCNVWord[];
  keywordAnswer?: string; // Chỉ có với sort = 9 (từ khóa dọc)
  stage: number;
  competenceId: string;
  hint: string;
  audioUrl?: string;      // URL file audio (optional)
}

// Interface cho crossword grid
export interface VCNVCrosswordData {
  horizontalQuestions: VCNVQuestion[];  // 8 câu hàng ngang
  keywordQuestion: VCNVQuestion;        // 1 câu từ khóa dọc
  gridLayout: VCNVGridCell[][];         // Layout lưới 8x9
}

export interface VCNVGridCell {
  row: number;
  col: number;
  questionId?: string;    // ID câu hỏi sở hữu ô này
  wordIndex?: number;     // Vị trí từ trong câu hỏi
  isKeyColumn: boolean;   // Có phải cột key không
  isActive: boolean;      // Có được sử dụng không
}

// API request/response types
export interface GetVCNVQuestionsRequest {
  gameId: string;
  stage: number;
  competenceId: string;
}

export interface GetVCNVQuestionsResponse extends ApiResponse<VCNVGameQuestionAPI> {}

export interface GetVCNVAnswersRequest {
  questionIds: string[];
}

export interface GetVCNVAnswersResponse extends ApiResponse<VCNVGameAnswerAPI> {}

export interface GetVCNVConfigRequest {
  gameId: string;
}

export interface GetVCNVConfigResponse extends ApiResponse<VCNVGameConfigAPI> {}

// Error types
export interface VCNVError {
  code: string;
  message: string;
  details?: any;
}

// Game state types
export interface VCNVGameState {
  // API Data
  questions: VCNVQuestion[];
  gameConfig: VCNVGameConfig | null;
  crosswordData: VCNVCrosswordData | null;

  // Current Game State
  currentHorizontalQuestion: VCNVQuestion | null;
  completedQuestions: string[];           // IDs của câu hỏi đã hoàn thành
  currentKeywordAnswer: string;           // Đáp án từ khóa hiện tại

  // Grid State
  gridCells: VCNVGridCell[][];
  wordsInGrid: {[questionId: string]: VCNVWord[]};

  // Game Progress
  questionDone: number;
  totalQuestion: number;
  currentStage: number;

  // Scoring & Config (from GameConfig API)
  maxLives: number;
  currentLives: number;
  timeLimit: number;
  timeRemaining: number;
  scorePerLife: number;
  bonusScore: number;
  currentScore: number;

  // API State
  loading: boolean;
  configLoading: boolean;
  error: VCNVError | null;
  configError: VCNVError | null;
  initialized: boolean;
  configInitialized: boolean;
}

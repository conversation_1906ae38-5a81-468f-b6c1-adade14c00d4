import {useDispatch} from 'react-redux';
import {startGame, startGameWithAPI, setData, setGameParams, resetGame, markHintUsed} from '../reducers/VTNVReducer';
import {AppDispatch} from '../../../../../redux/store/store';
import { loadGameConfig, loadGameData } from '../asyncThunk/vcnvAsyncThunk';

export const useVcnvHook = () => {
  const dispatch = useDispatch<AppDispatch>();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    setGameParams: (params: {gameId: string; stage: number; competenceId: string}) => {
      dispatch(setGameParams(params));
    },
    startGame: () => {
      dispatch(startGame());
    },
    startGameWithAPI: () => {
      dispatch(startGameWithAPI());
    },
    resetGame: () => {
      dispatch(resetGame());
    },
    loadGameConfig: (gameId: string) => {
      return dispatch(loadGameConfig(gameId));
    },
    loadGameData: (params: {gameId: string; milestoneId: number; competenceId: string}) => {
      return dispatch(loadGameData(params));
    },
    markHintUsed: (questionId: string) => {
      dispatch(markHintUsed(questionId));
    },
    // Combined action to load both config and data
    initializeGame: async (params: {gameId: string; milestoneId: number; competenceId: string}) => {
      try {
        // Set game parameters
        dispatch(setGameParams(params));

        // Load config and data in parallel
        const [configResult, dataResult] = await Promise.all([
          dispatch(loadGameConfig(params.gameId)),
          dispatch(loadGameData(params))
        ]);

        // Check if both succeeded
        if (configResult.type.endsWith('/fulfilled') && dataResult.type.endsWith('/fulfilled')) {
          // Auto start game with API data
          dispatch(startGameWithAPI());
          return { success: true };
        } else {
          throw new Error('Failed to initialize game');
        }
      } catch (error) {
        console.error('[useVcnvHook] Error initializing game:', error);
        return { success: false, error };
      }
    },
  };

  return action;
};

import React, {useEffect, useRef} from 'react';
import {useNavigation, useRoute} from '@react-navigation/native';
import TitleWithBackAction from '../Layout/titleWithBackAction';
import ByCategoryLoadmore from '../../modules/Course/listview/ByCategoryLoadmore';
import {
  AppButton,
  FBottomSheet,
  FPopup,
  showBottomSheet,
  showPopup,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {SearchIndex} from '../../modules/Course/listview/search';
// import { useLanguage } from '../../locales/languageContext';

const ListCoursebyCate = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const {title, id} = route.params;
  const bottomSheetRef = useRef<any>(null);

  //   const {t} = useTranslation();
  // const { changeLanguage } = useLanguage();

  useEffect(() => {});
  return (
    <TitleWithBackAction
      // titleBottom={title}
      onBack={() => {
        navigation.goBack();
      }}
      // iconAction="outline/user interface/setup-tools"
      // iconActionPress={() => {}}
      action={
        <AppButton
          backgroundColor={'transparent'}
          borderColor="transparent"
          onPress={() => {
            showBottomSheet({
              ref: bottomSheetRef,
              enableDismiss: true,
              children: <SearchIndex ref={bottomSheetRef} />,
            });
          }}
          containerStyle={{
            borderRadius: 100,
            padding: 6,
            height: 32,
            width: 32,
            backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
          }}
          title={
            <Winicon
              src={'outline/user interface/search'}
              size={18}
              color={ColorThemes.light.Neutral_Text_Color_Title}
            />
          }
        />
      }>
      <FBottomSheet ref={bottomSheetRef} />
      {/* Main content */}
      {/* <ScrollView style={styles.container}> */}
      <ByCategoryLoadmore id={id} titleList={title} />
      {/* </ScrollView> */}
    </TitleWithBackAction>
  );
};

export default ListCoursebyCate;

import React from 'react';
import {TouchableOpacity, Text, StyleSheet} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';

const MemoizedWinicon = React.memo(Winicon);

interface TabItemProps {
  tab: {
    Id: number;
    Name: string;
    Icon: string;
  };
  index: number;
  isActive: boolean;
  onPress: () => void;
}

const TabItem = React.memo(({tab, index, isActive, onPress}: TabItemProps) => (
  <TouchableOpacity
    key={index}
    onPress={onPress}
    style={[styles.tab, isActive && styles.activeTab]}>
    <MemoizedWinicon key={tab.Icon} src={tab.Icon} size={12} />
    <Text style={[styles.tabText]}>{tab.Name}</Text>
  </TouchableOpacity>
));

export default TabItem;

const styles = StyleSheet.create({
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
    borderWidth: 1,
  },
  activeTab: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderWidth: 0,
  },
  tabText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginLeft: 4,
  },
});

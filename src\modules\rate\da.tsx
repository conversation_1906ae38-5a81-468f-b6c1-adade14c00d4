import {DataController} from '../../base/baseController';
import { StorageContanst } from '../../Config/Contanst';
import { getDataToAsyncStorage } from '../../utils/AsyncStorage';
  export const getLikesRatingCourse = async (id: string) => {
    const courseController = new DataController('Like_Rating');
    const courseResult = await courseController.getListSimple({
      query: `@RatingId: {${id}}`,
    });
    if (courseResult.code === 200) {
      return courseResult.data?.length ?? 0;
    }
    return 0;
  };
  export const getIsLikeRating = async (id: string) => {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const customer = new DataController('Customer');
    const customerResult = await customer.getListSimple({
      query: `@RatingId:{${id}} @CustomerId:{${cusId}}`,
    });
    if (customerResult?.data) {
      return true;
    }
    return false;
  };

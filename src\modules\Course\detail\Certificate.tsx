import {useNavigation, useRoute} from '@react-navigation/native';
import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  Button,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import ViewShot, {captureRef} from 'react-native-view-shot';
import {
  ComponentStatus,
  FLoading,
  showSnackbar,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {CourseDA} from '../da';
import {BaseDA} from '../../../base/BaseDA';
// import RNFS from 'react-native-fs';
export default function CertificateScreen() {
  const viewShotRef = useRef(null);
  const customer = useSelectorCustomerState().data;
  const [isLoading, setLoading] = useState(false);
  const route = useRoute<any>();
  const navigation = useNavigation<any>();
  const courseDA = new CourseDA();
  const {
    courseId,
    courseName,
    certificateName,
    author,
    customerName,
    dateCompleted,
    CerticateNo,
    signature,
    isCertificate,
  } = route.params;
  const generateCertificate = async () => {
    try {
      setLoading(true);
      // Capture certificate view as image
      const uri = await captureRef(viewShotRef, {
        format: 'png',
        quality: 1,
      });

      // Prepare file object for upload
      const fileName = `certificate_${courseId}_${new Date().getTime()}.png`;
      const fileToUpload = [
        {
          uri: uri,
          type: 'image/png',
          name: fileName,
        },
      ];

      // Upload file
      const uploadResult = await BaseDA.uploadFiles(fileToUpload);
      if (uploadResult?.length > 0) {
        const certificateData = {
          Id: courseId,
          CerticateNo: CerticateNo,
          Name: certificateName,
          DateCreated: new Date().getTime(),
          CustomerName: customerName,
          CustomerId: customer.Id,
          CourseId: courseId,
          CertificateImage: uploadResult[0].Id, // Save the uploaded file ID
        };

        const result = await courseDA.addCertificate(certificateData);

        if (result?.code === 200) {
          showSnackbar({
            message: 'Tạo chứng chỉ thành công',
            status: ComponentStatus.SUCCSESS,
          });

          // Optional: Download the certificate
          // const downloadUrl = ConfigAPI.urlImg + uploadResult[0].Id;
          // await downloadCertificate(downloadUrl, fileName);
        } else {
          throw new Error('Failed to save certificate data');
        }
      } else {
        throw new Error('Failed to upload certificate image');
      }
    } catch (error) {
      console.error('Error generating certificate:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi tạo chứng chỉ',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  // const downloadCertificate = async (url: string, fileName: string) => {
  //   try {
  //     // Kiểm tra quyền truy cập trên Android
  //     if (Platform.OS === 'android') {
  //       const granted = await PermissionsAndroid.request(
  //         PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
  //         {
  //           title: 'Quyền truy cập bộ nhớ',
  //           message: 'Ứng dụng cần quyền truy cập bộ nhớ để tải chứng chỉ',
  //           buttonNeutral: 'Hỏi lại sau',
  //           buttonNegative: 'Hủy',
  //           buttonPositive: 'OK',
  //         }
  //       );
  //       if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
  //         throw new Error('Storage permission denied');
  //       }
  //     }

  //     // Xác định đường dẫn lưu file
  //     const downloadDir = Platform.OS === 'ios'
  //       ? RNFS.DocumentDirectoryPath
  //       : RNFS.DownloadDirectoryPath;

  //     const filePath = `${downloadDir}/${fileName}`;

  //     // Download file
  //     const response = await RNFS.downloadFile({
  //       fromUrl: url,
  //       toFile: filePath,
  //       background: true, // Cho phép tải về khi app ở background
  //       discretionary: true, // iOS: Tối ưu thời điểm tải về
  //       progress: (res) => {
  //         const progress = (res.bytesWritten / res.contentLength) * 100;
  //         console.log(`Download progress: ${progress}%`);
  //       },
  //     }).promise;

  //     if (response.statusCode === 200) {
  //       showSnackbar({
  //         message: 'Tải chứng chỉ thành công',
  //         status: ComponentStatus.SUCCSESS,
  //       });

  //       // Làm mới thư viện media trên Android
  //       if (Platform.OS === 'android') {
  //         await RNFS.scanFile(filePath);
  //       }
  //     } else {
  //       throw new Error('Download failed');
  //     }
  //   } catch (error) {
  //     console.error('Error downloading certificate:', error);
  //     showSnackbar({
  //       message: 'Có lỗi xảy ra khi tải chứng chỉ',
  //       status: ComponentStatus.ERROR,
  //     });
  //   }
  // };
  return isLoading ? (
    <FLoading visible={true} avt={require('../../../assets/appstore.png')} />
  ) : (
    <View style={styles.container}>
      <ViewShot ref={viewShotRef} options={{format: 'png', quality: 1}}>
        <View style={styles.certificate}>
          {/* Logo */}
          {/* <SkeletonImage
            source={{ uri: 'https://yourdomain.com/logo.png' }}
            style={styles.logo}
            resizeMode="contain"
          /> */}
          <Winicon src={'color/education/book-open'} size={40} />
          {/* Title */}
          <Text style={styles.title}>Certificate of Completion</Text>

          {/* Presented to */}
          <Text style={styles.subtitle}>This is proudly presented to</Text>
          <Text style={styles.name}>{customerName}</Text>

          {/* Course */}
          <Text style={styles.course}>for successfully completing</Text>
          <Text style={styles.courseName}>{courseName}</Text>

          {/* Date & Certificate ID */}
          <View style={styles.footer}>
            <View>
              <Text style={styles.footerLabel}>Date</Text>
              <Text style={styles.footerValue}>{dateCompleted}</Text>
            </View>

            <View style={{alignItems: 'center'}}>
              <Text style={styles.footerLabel}>Certificate No</Text>
              <Text style={styles.footerValue}>{CerticateNo}</Text>
            </View>

            {/* QR Code */}
          </View>

          {/* Signature */}
          <View style={styles.signatureBlock}>
            <Image
              source={{uri: ConfigAPI.urlImg + signature}} // chữ ký giảng viên
              style={styles.signature}
              resizeMode="contain"
            />
            <Text style={styles.signatureName}>Giảng viên</Text>
          </View>
        </View>
      </ViewShot>
      {isCertificate ? (
        <></>
      ) : (
        <Button title="Nhận chứng chỉ" onPress={generateCertificate} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ddd',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  certificate: {
    width: 350,
    height: 500,
    backgroundColor: '#fff',
    borderColor: '#000',
    borderWidth: 2,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 10,
  },
  logo: {
    width: 100,
    height: 50,
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#222',
  },
  subtitle: {
    fontSize: 16,
    color: '#777',
    marginTop: 8,
  },
  name: {
    fontSize: 22,
    fontWeight: 'bold',
    marginTop: 8,
    color: '#000',
  },
  course: {
    fontSize: 16,
    marginTop: 12,
    color: '#666',
  },
  courseName: {
    fontSize: 18,
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
  },
  footer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingHorizontal: 10,
  },
  footerLabel: {
    fontSize: 12,
    color: '#999',
  },
  footerValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
  },
  signatureBlock: {
    alignItems: 'center',
    marginTop: 10,
  },
  signature: {
    width: 120,
    height: 40,
  },
  signatureName: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
});

import React, {useState} from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import PDFViewer from './PDFViewer';
import ConfigAPI from '../../../Config/ConfigAPI';

const PDFDebugger: React.FC = () => {
  const [testUrl, setTestUrl] = useState(
    '/uploads/2025/5/ngo_tien_liem_cv.pdf',
  );
  const [showPDF, setShowPDF] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebugLog = (message: string) => {
    setDebugInfo(prev => [
      ...prev,
      `${new Date().toLocaleTimeString()}: ${message}`,
    ]);
  };

  const testURL = async () => {
    addDebugLog('Testing URL: ' + testUrl);

    // Test 1: Check if URL is accessible
    const fullUrl = ConfigAPI.url.replace('/api/', '') + testUrl;
    addDebugLog('Full URL: ' + fullUrl);

    try {
      const response = await fetch(fullUrl, {method: 'HEAD'});
      addDebugLog(`HTTP Status: ${response.status}`);
      addDebugLog(`Content-Type: ${response.headers.get('content-type')}`);
      addDebugLog(
        `Content-Length: ${response.headers.get('content-length')} bytes`,
      );

      if (response.status === 200) {
        addDebugLog('✅ URL is accessible');
        setShowPDF(true);
      } else {
        addDebugLog('❌ URL returned error status');
        Alert.alert('Error', `HTTP ${response.status}: URL không thể truy cập`);
      }
    } catch (error) {
      addDebugLog('❌ Network error: ' + error);
      Alert.alert('Error', 'Không thể kết nối đến URL: ' + error);
    }
  };

  const clearLogs = () => {
    setDebugInfo([]);
  };

  const testCommonURLs = () => {
    const commonUrls = [
      '/uploads/2025/5/ngo_tien_liem_cv.pdf',
      'https://redis.ktxgroup.com.vn/uploads/2025/5/ngo_tien_liem_cv.pdf',
      'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
    ];

    commonUrls.forEach(url => {
      addDebugLog(`Testing common URL: ${url}`);
    });
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>PDF Debugger</Text>

      {/* URL Input */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test URL</Text>
        <TextInput
          style={styles.input}
          value={testUrl}
          onChangeText={setTestUrl}
          placeholder="Enter PDF URL (relative or absolute)"
          multiline
        />
        <View style={styles.buttonRow}>
          <TouchableOpacity style={styles.button} onPress={testURL}>
            <Winicon src="outline/user interface/play" size={16} color="#fff" />
            <Text style={styles.buttonText}>Test URL</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.buttonSecondary}
            onPress={testCommonURLs}>
            <Text style={styles.buttonSecondaryText}>Test Common URLs</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Debug Logs */}
      <View style={styles.section}>
        <View style={styles.logHeader}>
          <Text style={styles.sectionTitle}>Debug Logs</Text>
          <TouchableOpacity style={styles.clearButton} onPress={clearLogs}>
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.logContainer}>
          {debugInfo.length === 0 ? (
            <Text style={styles.emptyLog}>No logs yet...</Text>
          ) : (
            debugInfo.map((log, index) => (
              <Text key={index} style={styles.logText}>
                {log}
              </Text>
            ))
          )}
        </View>
      </View>

      {/* PDF Viewer */}
      {showPDF && (
        <View style={styles.section}>
          <View style={styles.pdfHeader}>
            <Text style={styles.sectionTitle}>PDF Viewer</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowPDF(false)}>
              <Winicon src="outline/user interface/e-remove" size={16} />
            </TouchableOpacity>
          </View>
          <PDFViewer
            url={testUrl}
            fileName="Test PDF"
            height={400}
            maxFileSize={20}
            enableOptimization={true}
            onError={error => {
              addDebugLog('❌ PDF Viewer Error: ' + JSON.stringify(error));
            }}
            onLoadStart={() => {
              addDebugLog('🔄 PDF loading started');
            }}
            onLoadEnd={() => {
              addDebugLog('✅ PDF loading completed');
            }}
          />
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    padding: 16,
  },
  title: {
    ...TypoSkin.title2,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 20,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  sectionTitle: {
    ...TypoSkin.title3,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 12,
  },
  input: {
    ...TypoSkin.body2,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
    borderRadius: 6,
    padding: 12,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    marginBottom: 12,
    minHeight: 60,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 6,
    gap: 8,
  },
  buttonText: {
    ...TypoSkin.body2,
    color: '#fff',
    fontWeight: '600',
  },
  buttonSecondary: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  buttonSecondaryText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  logHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    backgroundColor: ColorThemes.light.Error_Color_Main,
  },
  clearButtonText: {
    ...TypoSkin.body2,
    color: '#fff',
    fontSize: 12,
  },
  logContainer: {
    backgroundColor: '#000',
    borderRadius: 6,
    padding: 12,
    maxHeight: 200,
  },
  emptyLog: {
    ...TypoSkin.body2,
    color: '#888',
    fontStyle: 'italic',
  },
  logText: {
    ...TypoSkin.body2,
    color: '#00ff00',
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  pdfHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  closeButton: {
    padding: 8,
    borderRadius: 4,
    backgroundColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
});

export default PDFDebugger;

import {t} from 'i18next';
import {
  FlatList,
  View,
  ActivityIndicator,
  Text,
  TouchableOpacity,
} from 'react-native';
import {
  AppButton,
  ComponentStatus,
  FDialog,
  HashTag,
  ListTile,
  showDialog,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import TitleWithBackAction from '../../../../Screen/Layout/titleWithBackAction';
import {useEffect, useRef, useState} from 'react';
import {navigate, RootScreen} from '../../../../router/router';
import {TypoSkin} from '../../../../assets/skin/typography';
import {StatusExam} from '../../../../Config/Contanst';
import {examDA} from '../../da';
import {Ultis} from '../../../../utils/Utils';
import EmptyPage from '../../../../Screen/emptyPage';
import {RefreshControl} from 'react-native-gesture-handler';
import {useRoute} from '@react-navigation/native';

export default function HistoryTryingList() {
  const [isLoading, setLoading] = useState(false);
  const [isRefresh, setRefresh] = useState(false);
  const dialogRef = useRef<any>(null);
  const [data, setData] = useState<Array<any>>([]);
  const exam = new examDA();
  const route = useRoute<any>();
  const idsExam = route.params?.idsExam;
  useEffect(() => {
    getData();
  }, [isRefresh]);

  const getData = async () => {
    if (!idsExam) return;
    setLoading(true);
    
    const result = await exam.getListExamHistory({ids: idsExam});
    if (result) {
      setData(result.data);
    }
    setLoading(false);
    setRefresh(false);
  };
  return (
    <TitleWithBackAction>
      <FDialog ref={dialogRef} />
      <View
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}>
        <FlatList
          nestedScrollEnabled
          data={data}
          refreshControl={
            <RefreshControl
              refreshing={isRefresh}
              onRefresh={() => setRefresh(!isRefresh)}
            />
          }
          ListHeaderComponent={() => {
            return (
              <View style={{paddingHorizontal: 16}}>
                <Text
                  style={{
                    ...TypoSkin.title2,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                  }}>
                  Lịch sử thi
                </Text>
              </View>
            );
          }}
          contentContainerStyle={{
            gap: 16,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}
          renderItem={({item, index}) => {
            return (
              <ListTile
                key={index}
                onPress={() => {
                  navigate(RootScreen.tryingHistoryTest, {
                    id: item.Id,
                  });
                }}
                style={{
                  borderColor: ColorThemes.light.Neutral_Border_Color_Main,
                  borderWidth: 1,
                  marginHorizontal: 16,
                  padding: 16,
                }}
                title={
                  <View style={{width: '100%', gap: 4}}>
                    {item.Status === StatusExam.passed ? (
                      <HashTag
                        title={t('exam.passed')}
                        textStyles={{
                          ...TypoSkin.title3,
                          color: ColorThemes.light.Success_Color_Main,
                        }}
                        styles={{
                          backgroundColor:
                            ColorThemes.light.Success_Color_Background,
                        }}
                      />
                    ) : (
                      <HashTag
                        title={t('exam.failed')}
                        textStyles={{
                          ...TypoSkin.title3,
                          color: ColorThemes.light.Error_Color_Main,
                        }}
                        styles={{
                          backgroundColor:
                            ColorThemes.light.Error_Color_Background,
                        }}
                      />
                    )}
                    {/* title */}
                    <Text
                      style={{
                        ...TypoSkin.heading7,
                        color: ColorThemes.light.Neutral_Text_Color_Title,
                      }}>
                      {`${item.Name ?? ''}`}
                    </Text>
                  </View>
                }
                subtitle={
                  <View
                    style={{
                      width: '100%',
                      paddingTop: 4,
                      alignContent: 'flex-start',
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.subtitle3,
                        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}>
                      {`${Ultis.datetoString(
                        new Date(item.DateCreated ?? 0),
                        'dd/MM/yyyy hh:mm',
                      )}`}
                    </Text>
                  </View>
                }
                bottom={
                  <View
                    style={{
                      paddingTop: 12,
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                      width: '100%',
                      gap: 4,
                    }}>
                    <AppButton
                      prefixIcon={'outline/editing/unordered-list'}
                      prefixIconSize={16}
                      title={t('exam.correctAnswersCount', {
                        correct: item.TotalAnswerCorrect ?? 0,
                        total: item.TotalQuestion ?? 0
                      })}
                      containerStyle={{alignSelf: 'baseline', height: 24}}
                      textStyle={{...TypoSkin.subtitle3}}
                      textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
                      borderColor="transparent"
                      backgroundColor={'transparent'}
                    />
                    <AppButton
                      prefixIcon={'outline/buildings/time-clock'}
                      prefixIconSize={16}
                      title={t('exam.timeMinutes', {time: item.Time ?? 0})}
                      containerStyle={{alignSelf: 'baseline', height: 24}}
                      textStyle={{...TypoSkin.subtitle3}}
                      textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
                      borderColor="transparent"
                      backgroundColor={'transparent'}
                    />
                  </View>
                }
                trailing={
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'flex-end',
                      gap: 16,
                    }}>
                    {/* <TouchableOpacity
                      style={{
                        padding: 8,
                        borderRadius: 100,
                        borderColor:
                          ColorThemes.light.Neutral_Border_Color_Main,
                        borderWidth: 1,
                      }}
                      onPress={() => {
                        showDialog({
                          ref: dialogRef,
                          status: ComponentStatus.WARNING,
                          title: 'Xoá bài thi này?',
                          onSubmit: async () => {},
                        });
                      }}>
                      <Winicon
                        src="outline/user interface/trash-can"
                        color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                        size={16}
                      />
                    </TouchableOpacity> */}
                  </View>
                }
              />
            );
          }}
          style={{width: '100%', height: '100%'}}
          // keyExtractor={a => a.Id?.toString()}
          onEndReachedThreshold={0.5}
          ListEmptyComponent={() => {
            if (isLoading) {
              return (
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}>
                  <ActivityIndicator
                    color={ColorThemes.light.Primary_Color_Main}
                  />
                </View>
              );
            }
            return <EmptyPage title={t('nodata')} />;
          }}
        />
      </View>
    </TitleWithBackAction>
  );
}

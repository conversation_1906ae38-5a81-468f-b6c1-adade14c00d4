// App.js

import React, {useEffect, useState, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {BackgroundData} from '../../../../redux/models/PostBackground';
import {useSelector} from 'react-redux';
import {RootState} from '../../../../redux/store/store';

interface SwatchItemProps {
  item: BackgroundData;
  onPress: () => void;
  isSelected: boolean;
}

interface PickColorLineProps {
  onChoose?: (selectedColor: BackgroundData) => void;
  isReset?: boolean;
}

const SwatchItem: React.FC<SwatchItemProps> = ({item, onPress, isSelected}) => {
  const renderContent = () => {
    switch (item.Type) {
      case 999:
        return <View style={styles.swatchEmpty} />;
      case 1:
        return (
          <View style={[styles.swatchBase, {backgroundColor: item.Color}]} />
        );
      case 2:
        // Parse gradient colors từ string "color1, color2" thành array
        const gradientColors = item.Color
          ? item.Color.split(',').map(color => color.trim())
          : ['#CCCCCC'];
        return (
          <LinearGradient colors={gradientColors} style={styles.swatchBase} />
        );
      case 3:
        return (
          <ImageBackground
            source={{uri: item.Img}}
            style={[styles.swatchBase, {backgroundColor: item.Color}]}>
            <Text style={styles.emojiText}>{item.TextColor}</Text>
          </ImageBackground>
        );
      default:
        return null;
    }
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.swatchContainer,
        // Thêm viền xanh để thể hiện mục đang được chọn
        isSelected && styles.selectedSwatch,
      ]}>
      {renderContent()}
    </TouchableOpacity>
  );
};

const PickColorLine: React.FC<PickColorLineProps> = ({onChoose, isReset}) => {
  const {items, loading} = useSelector(
    (state: RootState) => state.postBackground,
  );

  // Sử dụng items từ Redux store
  const backgroundData = useMemo(() => items, [items]);

  // Dùng useState với lazy initialization
  const [selectedId, setSelectedId] = useState<string>(() => {
    return items.length > 0 ? items[0].Id : '';
  });

  const handleColorSelect = (item: BackgroundData) => {
    setSelectedId(item.Id);
    onChoose?.(item);
  };

  // Cập nhật selectedId khi items được load lần đầu
  useEffect(() => {
    if (items.length > 0 && !selectedId) {
      setSelectedId(items[0].Id);
    }
  }, [items, selectedId]);

  useEffect(() => {
    if (isReset && backgroundData.length > 0) {
      setSelectedId(backgroundData[0].Id);
    }
  }, [isReset, backgroundData]);

  // Early return AFTER all hooks
  if (items.length <= 0) return null;

  // Hiển thị loading nếu đang tải data
  if (loading && items.length === 0) {
    return (
      <View style={styles.screen}>
        <View style={styles.container}>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>‹</Text>
          </TouchableOpacity>
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.screen}>
      <View style={styles.container}>
        {/* Nút điều hướng bên trái */}
        <TouchableOpacity style={styles.backButton}>
          <Text style={styles.backButtonText}>‹</Text>
        </TouchableOpacity>

        {/* Danh sách các lựa chọn */}
        <FlatList
          data={backgroundData}
          renderItem={({item}) => (
            <SwatchItem
              item={item}
              onPress={() => handleColorSelect(item)}
              isSelected={item.Id === selectedId}
            />
          )}
          keyExtractor={item => item.Id}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.flatList}
        />
      </View>
    </View>
  );
};

export default PickColorLine;

const SWATCH_SIZE = 44;
const SWATCH_MARGIN = 6;

const styles = StyleSheet.create({
  screen: {
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    backgroundColor: 'white',
    width: '100%',
  },
  backButton: {
    width: SWATCH_SIZE,
    height: SWATCH_SIZE,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#EAEAEA',
    borderRadius: 12,
    marginLeft: 10,
  },
  backButtonText: {
    fontSize: 28,
    color: '#555',
    lineHeight: 32,
  },
  flatList: {
    flex: 1,
    marginLeft: SWATCH_MARGIN,
  },
  swatchContainer: {
    width: SWATCH_SIZE,
    height: SWATCH_SIZE,
    marginHorizontal: SWATCH_MARGIN,
    borderRadius: 12,
    // Style cho viền khi được chọn
    borderWidth: 2,
    borderColor: 'transparent', // Mặc định trong suốt
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedSwatch: {
    borderColor: '#007AFF', // Màu viền xanh khi được chọn
  },
  swatchBase: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden', // Đảm bảo gradient không tràn ra ngoài
  },
  swatchEmpty: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#CCCCCC',
  },
  emojiText: {
    fontSize: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: SWATCH_MARGIN,
  },
  loadingText: {
    color: '#555',
    fontSize: 14,
  },
});

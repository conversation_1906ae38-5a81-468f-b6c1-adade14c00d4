// TypeScript interfaces cho SakuTB Game với cấu trúc mới

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T[];
  total?: number;
}

// Data từ bảng GameQuizQuestion
export interface GameQuizQuestionAPI {
  Id: string;
  GameId: string;
  Stage: number;
  Name: string;           // Nội dung câu hỏi
  Options: string;           // JSON string chứa 10 items (5 left + 5 right)
  CorrectAnswerIndex: string; // JSON string dạng ["1,6","2,7","3,8","4,9","5,10"]
  Sort: number;
  IsActive: boolean;
  CreatedAt?: string;
  UpdatedAt?: string;
}

// Option item sau khi parse từ JSON
export interface OptionItem {
  id: string;
  text: string;
  type: 'text' | 'audio' | 'image';
  index: number;  // Index trong mảng Options (1-10)
  sort?: number;  // Sort field từ GameAnswer
}

// Cặp đáp án đúng
export interface CorrectPair {
  leftIndex: number;   // Index của item bên trái (1-5)
  rightIndex: number;  // Index của item bên phải (6-10)
  matchId: string;     // ID để match cặp
}

// Item cho game (sau khi transform)
export interface SakuTBItem {
  id: string;
  text: string;
  type: 'text' | 'audio' | 'image';
  matchId: string;     // ID để match với cặp
  side: 'left' | 'right';
  originalIndex: number; // Index gốc trong Options (1-10)
}

// Question data sau khi transform cho game
export interface SakuTBQuestion {
  id: string;
  content: string;
  leftItems: SakuTBItem[];
  rightItems: SakuTBItem[];
  correctPairs: CorrectPair[];
}

// Game state interface
export interface SakuTBGameState {
  // API Data
  rawQuestions: GameQuizQuestionAPI[];
  questions: SakuTBQuestion[];
  gameConfig: GameConfig | null;

  // Current Question
  currentQuestionIndex: number;
  currentQuestion: SakuTBQuestion | null;

  // Game Items (current question)
  listItemsLeft: SakuTBItem[];
  listItemsRight: SakuTBItem[];

  // Game State
  selectedLeft: SakuTBItem | null;
  selectedRight: SakuTBItem | null;
  matchedPairs: string[];
  errorPairs: string[];
  listItemsDone: {listItems: SakuTBItem[]}[];

  // Progress
  questionDone: number;
  totalQuestion: number;
  currentStage: number;

  // Scoring & Config (from GameConfig API)
  maxLives: number;
  currentLives: number;
  timeLimit: number;
  timeRemaining: number;
  scorePerLife: number;
  bonusScore: number;
  currentScore: number;

  // API State
  loading: boolean;
  configLoading: boolean;
  error: string | null;
  configError: string | null;
  initialized: boolean;
  configInitialized: boolean;
}

// Fallback data structure
export interface FallbackData {
  questions: GameQuizQuestionAPI[];
  config: {
    maxLives: number;
    timeLimit: number;
  };
}

// Utility types
export interface ParsedOptions {
  leftItems: OptionItem[];
  rightItems: OptionItem[];
}

export interface ParsedCorrectAnswers {
  pairs: CorrectPair[];
}

// API request/response types
export interface GetQuestionsRequest {
  gameId: string;
  stage: number;
}

export interface GetQuestionsResponse extends ApiResponse<GameQuizQuestionAPI> {}

// GameConfig API interface
export interface GameConfigAPI {
  Id: string;
  GameId: string;
  Score: number;        // Điểm trên mỗi mạng
  LifeCount: number;    // Số mạng chơi
  Time: number;         // Thời gian chơi (giây)
  Bonus: number;        // Điểm bonus khi hoàn thành không mất mạng
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
}

// Transformed GameConfig for game use
export interface GameConfig {
  gameId: string;
  scorePerLife: number;
  maxLives: number;
  timeLimit: number;
  bonusScore: number;
  isActive: boolean;
}

// API request/response for GameConfig
export interface GetGameConfigRequest {
  gameId: string;
}

export interface GetGameConfigResponse extends ApiResponse<GameConfigAPI> {}

// Error types
export interface SakuTBError {
  type: 'API_ERROR' | 'PARSE_ERROR' | 'VALIDATION_ERROR';
  message: string;
  details?: any;
}

# Script để update biến PATH với các công cụ phát triển

Write-Host "=== Updating System PATH ===" -ForegroundColor Green

# Lấy PATH hiện tại
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
$paths = $currentPath -split ';' | Where-Object { $_ -ne '' }

# Danh sách các đường dẫn cần thêm
$newPaths = @(
    "C:\Program Files\Git\bin",
    "C:\Program Files\Git\cmd",
    "C:\nvm4w\nodejs",  # npm prefix location
    "C:\Users\<USER>\AppData\Roaming\npm",
    "C:\Users\<USER>\AppData\Local\Yarn\bin",
    "C:\Python\Python312",
    "C:\Python\Python312\Scripts",
    "C:\Program Files\nodejs",
    "C:\Program Files (x86)\Yarn\bin",
    "C:\ProgramData\chocolatey\bin",  # Chocolatey if installed
    "C:\tools\msys64\usr\bin",  # MSYS2/MinGW if installed
    "C:\Program Files\Docker\Docker\resources\bin",  # Docker
    "C:\Program Files\PostgreSQL\15\bin",  # PostgreSQL if installed
    "C:\Program Files\MySQL\MySQL Server 8.0\bin"  # MySQL if installed
)

# Kiểm tra và thêm các đường dẫn mới
foreach ($path in $newPaths) {
    if (Test-Path $path) {
        if ($paths -notcontains $path) {
            Write-Host "Adding: $path" -ForegroundColor Yellow
            $paths += $path
        } else {
            Write-Host "Already exists: $path" -ForegroundColor Cyan
        }
    } else {
        Write-Host "Not found: $path" -ForegroundColor Red
    }
}

# Cập nhật PATH
$newPath = $paths -join ';'
[Environment]::SetEnvironmentVariable("PATH", $newPath, "User")

Write-Host "`n=== PATH Updated Successfully ===" -ForegroundColor Green
Write-Host "Please restart your terminal for changes to take effect." -ForegroundColor Yellow

# Hiển thị PATH mới
Write-Host "`nNew PATH entries:" -ForegroundColor Magenta
$paths | ForEach-Object { Write-Host "  $_" }

# Kiểm tra Git
Write-Host "`n=== Testing Tools ===" -ForegroundColor Green
if (Get-Command git -ErrorAction SilentlyContinue) {
    $gitVersion = git --version
    Write-Host "Git: $gitVersion" -ForegroundColor Green
} else {
    Write-Host "Git: NOT FOUND - Please restart terminal" -ForegroundColor Red
} 
import {Image, StyleSheet, Text, View, ScrollView, TouchableOpacity} from 'react-native';
import {useTranslation} from 'react-i18next';
import {
  AppButton,
  FBottomSheet,
  showSnackbar,
  ComponentStatus,
  Winicon,
} from 'wini-mobile-components';
import {navigateBack} from '../../../router/router';
import {ColorThemes} from '../../../assets/skin/colors';
import {useRoute, RouteProp} from '@react-navigation/native';
import {useEffect, useRef, useState, useCallback, useMemo} from 'react';
import VideoPlayerWithFullscreen from '../components/VideoPlayerWithFullscreen';
import {CourseDA} from '../da';
import {SafeAreaView} from 'react-native-safe-area-context';
import {TypoSkin} from '../../../assets/skin/typography';
import {BaseDA} from '../../../base/BaseDA';
import {TabBar, TabView} from 'react-native-tab-view';
import PDFViewer from '../components/PDFViewer';
import {
  ListTile,
} from 'wini-mobile-components';
import IntroductionLesson from './IntroLesson';
import { FlashCardCourse } from './flashCard';
import OverviewTest from '../../exam/views/overviewQuiz';
import { LoadingUI } from '../../../features/loading';
import VideoDownloadButton from '../components/VideoDownloadButton';


// Video Navigation Bar Component
interface VideoNavigationBarProps {
  videos: VideoData[];
  currentVideoIndex: number;
  onVideoSelect: (index: number) => void;
  completedVideos: Set<number>;
}

const VideoNavigationBar: React.FC<VideoNavigationBarProps> = ({
  videos,
  currentVideoIndex,
  onVideoSelect,
  completedVideos,
}) => {
  if (videos.length <= 1) {
    return null;
  }

  return (
    <View style={styles.videoNavigationContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.videoNavigationContent}
      >
        {videos.map((_, index) => {
          const isActive = index === currentVideoIndex;
          const isCompleted = completedVideos.has(index);

          return (
            <TouchableOpacity
              key={index}
              style={[
                styles.videoNavButton,
                isActive && styles.videoNavButtonActive,
                isCompleted && styles.videoNavButtonCompleted,
              ]}
              onPress={() => onVideoSelect(index)}
            >
              <Text style={[
                styles.videoNavButtonText,
                isActive && styles.videoNavButtonTextActive,
                isCompleted && styles.videoNavButtonTextCompleted,
              ]}>
                {index + 1}
              </Text>
              {isCompleted && !isActive && (
                <View style={styles.completedIndicator}>
                  <Winicon
                    src="fill/user interface/check"
                    size={8}
                    color={ColorThemes.light.Success_Color_Main}
                  />
                </View>
              )}
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
};

// TypeScript Interfaces
interface StepData {
  lessonId: string;
  courseId: string;
  lessonIndex: number;
  name?: string;
  videoIds?: string[];
  stepOrder?: number;
}

interface LessonData {
  Id: string;
  Name: string;
  Video?: string;
  Document?: string;
  Introduction?: string;
  Hours?: number;
  FlashCardId?: string;
  ExamId?: string;
  Description?: string;

}

interface VideoData {
  Id: number;
  Url: string;
  Name: string;
  isCompleted?: boolean;
}

interface RouteParams {
  Step: StepData;
  type: 'Video' | 'Document';
}

type LearnCourseRouteProp = RouteProp<{params: RouteParams}, 'params'>;

export default function LearnCourse() {
  const {t} = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [videoLoading, setVideoLoading] = useState(false);
  const route = useRoute<LearnCourseRouteProp>();
  const {Step} = route.params;

  const [lessonData, setLessonData] = useState<LessonData | null>(null);
  const courseDA = useMemo(() => new CourseDA(), []);

  const fetchData = useCallback(async () => {
    if (!Step?.lessonId) {
      setError(t('course.lessonNotFound') || 'Lesson not found');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Fetch lesson data
      const lessonResult = await courseDA.getLessonDetail(Step.lessonId);

      if (lessonResult && lessonResult.code === 200 && lessonResult.data) {
        setLessonData(lessonResult.data);
      } else {
        setError(t('course.cannotLoadLesson'));
        showSnackbar({
          message: t('course.cannotLoadLesson'),
          status: ComponentStatus.ERROR,
        });
      }
    } catch (fetchError) {
      console.error('Error fetching data:', fetchError);
      const errorMessage = t('course.loadingError');
      setError(errorMessage);
      showSnackbar({
        message: errorMessage,
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  }, [Step?.lessonId, courseDA]);


  // Fetch data when Step changes
  useEffect(() => {
    fetchData();
  }, [fetchData]);


  const dispatchedRef = useRef<Set<number>>(new Set());
  const bottomSheetRef = useRef<any>(null);
  const hasCheckedInitialVideo = useRef<boolean>(false);
  // const playerRef = useRef<VideoPlayerRef>(null);

  const [videos, setVideos] = useState<VideoData[]>([]);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [completedVideos, setCompletedVideos] = useState<Set<number>>(
    new Set(),
  );
  const [tabIndex, setTabIndex] = useState(0); // Mặc định vào tab đầu tiên (video)

  // Document states
  const [documents, setDocuments] = useState<any[]>([]);
  const [documentViewMode, setDocumentViewMode] = useState<'list' | 'pdf'>('pdf');
  const [currentDocumentIndex, setCurrentDocumentIndex] = useState(0);
  const [documentLoading, setDocumentLoading] = useState(false);

  // Current video for backward compatibility
  const currentVideo = videos[currentVideoIndex] || null;

  // Tab routes - Video tab first as requested
  const tabRoutes = useMemo(
    () => {
      var list = [];
      // Video tab luôn đầu tiên nếu có video
      if(lessonData?.Video){
        list.push({key: 'videos', title: 'Video'});
      }
      if(lessonData?.Introduction){
        list.push({key: 'introduction', title: t('course.introduction')});
      }
      if(lessonData?.FlashCardId){
        list.push({key: 'flashcard', title: t('course.vocabulary')});
      }
      if(lessonData?.ExamId){
        list.push({key: 'exam', title: t('course.review')});
      }
      return list;

    },
    [lessonData],
  );



  // Document helper functions
  const isPDFFile = (url: string): boolean => {
    return url.toLowerCase().includes('.pdf');
  };

  const currentDocument = documents.length > 0 ? documents[currentDocumentIndex] : null;
  const canShowPDF = currentDocument && isPDFFile(currentDocument.url);

  // Tab render functions
  const renderTabScene = useCallback(
    ({route: tabRoute}: any) => {
      switch (tabRoute.key) {
        case 'videos':
          return (
            <View style={styles.videoTabContainer}>
              {/* Fixed Video Section */}
              <View style={styles.fixedVideoSection}>
                {/* Video Player */}
                <View style={styles.videoPlayerContainer}>
                  {videoLoading ? (
                    <View style={styles.placeholderContainer}>
                      <LoadingUI isLoading={videoLoading} />
                    </View>
                  ) : currentVideo?.Url ? (
                    <VideoPlayerWithFullscreen
                      key={`video-player-${currentVideo.Id || 'unknown'}-lesson-${
                        Step.lessonId
                      }-index-${currentVideoIndex}`}
                      source={currentVideo.Url}
                      onProgressPercent={handleVideoProgress}
                      onLoad={time => {
                        console.log('Video loaded, duration:', time);
                      }}
                    />
                  ) : (
                    <View style={styles.placeholderContainer}>
                      <Image
                        style={styles.placeholderImage}
                        source={require('../../../assets/appstore.png')}
                      />
                      <Text style={styles.noVideoText}>{t('course.noVideo')}</Text>
                    </View>
                  )}
                </View>

                {/* Video Navigation Bar with Download Button */}
                <View style={styles.videoNavigationWrapper}>
                  <VideoNavigationBar
                    videos={videos}
                    currentVideoIndex={currentVideoIndex}
                    onVideoSelect={(index) => {
                      setCurrentVideoIndex(index);
                      // Reset progress tracking for new video
                      dispatchedRef.current.clear();
                    }}
                    completedVideos={completedVideos}
                  />

                  {/* Download Button for Current Video */}
                  {currentVideo?.Url && lessonData && Step && (
                    <View style={styles.currentVideoDownloadContainer}>
                      <VideoDownloadButton
                        videoUrl={currentVideo.Url}
                        lessonId={Step.lessonId}
                        lessonName={lessonData.Name || 'Unknown Lesson'}
                        courseId={Step.courseId}
                        videoName={currentVideo.Name || `Video ${currentVideoIndex + 1}`}
                        videoIndex={currentVideoIndex}
                        size="medium"
                      />
                    </View>
                  )}
                </View>
              </View>

              {/* Scrollable Document Section */}
              {(lessonData?.Document || documentLoading) && (
                <View style={styles.scrollableDocumentSection}>
                  {/* Floating button for document list */}
                  {(
                    <AppButton
                      containerStyle={styles.floatingButton}
                      backgroundColor={ColorThemes.light.transparent}
                      borderColor="transparent"
                      title={
                        <Winicon
                          src="outline/text/ordered-list"
                          size={24}
                          color={ColorThemes.light.Primary_Color_Main}
                        />
                      }
                      onPress={() => setDocumentViewMode('list')}
                    />
                  )}

                  {/* Document Content */}
                  {documentLoading ? (
                    // Loading state
                    <View style={styles.documentLoadingContainer}>
                      <LoadingUI isLoading={documentLoading} />
                      <Text style={styles.loadingText}>{t('course.loadingDocument')}</Text>
                    </View>
                  ) : documents.length === 0 ? (
                    // No documents
                    <View style={styles.documentLoadingContainer}>
                      <Winicon
                        src="outline/files/document"
                        size={48}
                        color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                      />
                      <Text style={styles.loadingText}>
                        {t('course.noDocument')}
                      </Text>
                    </View>
                  ) : documentViewMode === 'pdf' && canShowPDF ? (
                    // PDF View
                    <View style={styles.pdfViewContainer}>
                      {(() => {
                        console.log('=== RENDERING PDF VIEWER ===');
                        console.log('PDF URL:', currentDocument.url);
                        console.log('PDF Height:', getAvailablePDFHeight());
                        console.log('PDF fileName:', currentDocument.name);
                        return null;
                      })()}
                      <PDFViewer
                        key={`pdf-viewer-${currentDocumentIndex}`}
                        url={currentDocument.url}
                        fileName={currentDocument.name || t('course.pdfDocument')}
                        height={getAvailablePDFHeight()}
                        maxFileSize={15}
                        enableOptimization={true}
                        useGoogleViewer={true}
                        onError={(pdfError) => {
                          console.error('PDF Viewer error:', pdfError);
                          setDocumentViewMode('list');
                        }}
                        onLoadStart={() => {
                          console.log('PDF loading started for:', currentDocument.name);
                        }}
                        onLoadEnd={() => {
                          console.log('PDF loading completed for:', currentDocument.name);
                        }}
                      />
                    </View>
                  ) : documentViewMode === 'list' ? (
                    // List View
                    <ScrollView
                      style={styles.documentListContainer}
                      contentContainerStyle={styles.documentListContent}
                    >
                      {documents.map((item: any, index: number) => {
                        const isCurrentDocument = index === currentDocumentIndex;
                        return (
                          <View
                            key={index}
                            style={[
                              styles.documentItem,
                              isCurrentDocument && styles.documentItemActive,
                            ]}
                          >
                            <ListTile
                              onPress={() => selectDocument(index)}
                              leading={
                                <View
                                  style={[
                                    styles.documentIconContainer,
                                    isCurrentDocument && styles.documentIconContainerActive,
                                  ]}
                                >
                                  <Winicon
                                    src="fill/files/document-2"
                                    size={24}
                                    color={
                                      isCurrentDocument
                                        ? ColorThemes.light.Neutral_Background_Color_Absolute
                                        : ColorThemes.light.Primary_Color_Main
                                    }
                                  />
                                  {isCurrentDocument && (
                                    <View style={styles.currentIndicator}>
                                      <Winicon
                                        src="outline/user interface/check"
                                        size={12}
                                        color={ColorThemes.light.Success_Color_Main}
                                      />
                                    </View>
                                  )}
                                </View>
                              }
                              title={item.name || t('course.documentNumber', {number: index + 1})}
                              subtitle={
                                isCurrentDocument
                                  ? t('course.currentlyViewing')
                                  : item.description || t('course.tapToView')
                              }
                            />
                          </View>
                        );
                      })}
                    </ScrollView>
                  ) : (
                    // No PDF available - show message
                    <View style={styles.documentLoadingContainer}>
                      <Winicon
                        src="outline/files/document"
                        size={48}
                        color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                      />
                      <Text style={styles.loadingText}>
                        {documents.length > 0
                          ? t('course.documentNotSupported')
                          : t('course.noDocument')}
                      </Text>
                    </View>
                  )}
                </View>
              )}
            </View>
          );
        case 'introduction':
          return (
            <IntroductionLesson lessonData={lessonData} />
          );
          case 'flashcard':
            return (
              <FlashCardCourse lessonData={lessonData} />
            );
          case 'exam':
            return (
              <OverviewTest lessonData={lessonData} step={Step} />
            );
        default:
          return null;
      }
    },
    [
      lessonData,
      Step,
      tabIndex,
      tabRoutes,
      currentVideoIndex,
      videos,
      completedVideos,
      dispatchedRef,
      currentDocumentIndex,
      documents,
      documentViewMode,
      canShowPDF,
    ],
  );

  const renderTabBar = useCallback(
    (props: any) => (
      <TabBar
        {...props}
        indicatorStyle={styles.tabIndicator}
        style={styles.tabBar}
        tabStyle={styles.tabStyle}
        activeColor={ColorThemes.light.Primary_Color_Main}
        inactiveColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
      />
    ),
    [],
  );

  // Reset progress tracking when Step changes
  useEffect(() => {
    dispatchedRef.current.clear();
    hasCheckedInitialVideo.current = false; // Reset để kiểm tra lại video cho lesson mới
  }, [Step.lessonId]);

  const handleVideoProgress = useCallback(
    (percent: number) => {
      if (!Step) {
        return;
      }

      // Mark video as completed when reaching 90%
      if (percent >= 100) {
        setCompletedVideos(prev => new Set([...prev, currentVideoIndex]));
      }

      // Auto-next to next video when reaching 100%
      if (percent >= 100 && currentVideoIndex < videos.length - 1) {
        setTimeout(() => {
          setCurrentVideoIndex(prev => prev + 1);
          dispatchedRef.current.clear(); // Reset progress tracking
        }, 1000); // Small delay before auto-next
      }


    },
    [Step,  currentVideoIndex, videos.length, lessonData?.Video],
  );

  useEffect(() => {
    const loadVideos = async () => {
      if (!lessonData?.Video) {
        setVideos([]);
        return;
      }

      setVideoLoading(true);
      try {
        // Parse video IDs from comma-separated string
        const videoIds = lessonData.Video.split(',').map((id: string) =>
          id.trim(),
        );

        if (videoIds.length > 0) {
          // const rs = await BaseDA.getFilesInfor(videoIds);
          if (videoIds && videoIds.length > 0) {
            // Re-enable completion check safely

            const videoList = videoIds.map((item: any, index: number) => {
              return {
                Id: index,
                Url: item ?? '',
                Name: Step.name ?? `Video ${index + 1}`,
                isCompleted: completedVideos.has(index) || false,
              };
            });
            setVideos(videoList);
          } else {
            setVideos([]);
            console.warn('No video data received');
          }
        }
      } catch (videoError) {
        console.error('Error loading videos:', videoError);
        setVideos([]);
      } finally {
        setVideoLoading(false);
      }
    };
    loadVideos();
  }, [lessonData]);

  // Tự động chuyển đến video chưa hoàn thành đầu tiên khi videos được load lần đầu
  useEffect(() => {
    // Only run once when videos are first loaded
    if (videos.length > 0 && !hasCheckedInitialVideo.current) {
      hasCheckedInitialVideo.current = true;

      try {
        // Safely check if current video is completed
        const currentVideoData = videos[currentVideoIndex];
        if (currentVideoData && currentVideoData.isCompleted) {
          const firstIncompleteIndex = videos.findIndex(
            (video: VideoData) => !video.isCompleted,
          );
          if (
            firstIncompleteIndex !== -1 &&
            firstIncompleteIndex !== currentVideoIndex
          ) {
            console.log(
              `Auto-switching from completed video ${currentVideoIndex} to incomplete video ${firstIncompleteIndex}`,
            );
            setCurrentVideoIndex(firstIncompleteIndex);
          }
        }
      } catch (autoSwitchError) {
        console.warn('Error in auto-switch logic:', autoSwitchError);
      }
    }
  }, [videos.length]); // Only depend on videos.length to avoid infinite loops

  // Load documents when lessonData changes
  useEffect(() => {
    const loadDocuments = async () => {
      console.log('=== LOADING DOCUMENTS ===');
      console.log('lessonData:', lessonData);
      console.log('lessonData?.Document:', lessonData?.Document);

      if (lessonData && lessonData.Document) {
        setDocumentLoading(true);
        try {
          const ids = lessonData.Document.split(',').map(id => id.trim()).filter(id => id);
          console.log('Document IDs:', ids);

          if (ids.length > 0) {
            console.log('Calling BaseDA.getFilesInfor with IDs:', ids);
            const fileInfo = await BaseDA.getFilesInfor(ids);
            console.log('File info response:', fileInfo);
            console.log('File info type:', typeof fileInfo);
            console.log('File info data:', fileInfo?.data);

            if (fileInfo && fileInfo.data && Array.isArray(fileInfo.data)) {
              const documentList = fileInfo.data.map((item: any) => ({
                name: item.Name,
                url: item.Url,
                ...item,
              }));
              console.log('Processed documents:', documentList);
              setDocuments(documentList);
            } else {
              console.log('No file info data received or invalid format');
              setDocuments([]);
            }
          } else {
            console.log('No valid document IDs');
            setDocuments([]);
          }
        } catch (docError) {
          console.error('Error loading documents:', docError);
          setDocuments([]);
        } finally {
          setDocumentLoading(false);
        }
      } else {
        console.log('No lessonData or Document field');
        setDocuments([]);
        setDocumentLoading(false);
      }
    };
    loadDocuments();
  }, [lessonData]);

  // Debug logs (uncomment for debugging)
  console.log('=== DOCUMENT DEBUG ===');
  console.log('lessonData?.Document:', lessonData?.Document);
  console.log('documents.length:', documents.length);
  console.log('currentDocument:', currentDocument);
  console.log('canShowPDF:', canShowPDF);
  console.log('documentViewMode:', documentViewMode);
  console.log('currentDocumentIndex:', currentDocumentIndex);

  const selectDocument = (index: number) => {
    if (index === currentDocumentIndex) {
      setDocumentViewMode('pdf');
      return;
    }
    setCurrentDocumentIndex(index);
    setDocumentViewMode('pdf');
  };

  const getAvailablePDFHeight = () => {
    const statusBarHeight = 50;
    const headerHeight = 60;
    const tabBarHeight = 50;
    const videoPlayerHeight = 200;
    const videoNavigationHeight = 60;
    const floatingButtonSpace = 100;
    const padding = 32;

    const calculatedHeight =
      600 - // Use fixed height for now
      statusBarHeight -
      headerHeight -
      tabBarHeight -
      videoPlayerHeight -
      videoNavigationHeight -
      floatingButtonSpace -
      padding;
    const minHeight = 450;

    return Math.max(calculatedHeight, minHeight);
  };

  // Show error state if there's an error
  if (error) {
    return (
      <View style={styles.container}>
        <SafeAreaView edges={['top']} />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <AppButton
            title="Thử lại"
            onPress={fetchData}
            containerStyle={styles.retryButton}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FBottomSheet ref={bottomSheetRef} />
      <SafeAreaView edges={['top']} />
      {loading ? (
        <View style={styles.loadingContainer} pointerEvents="none">
          <LoadingUI isLoading={loading} />
        </View>
      ) : (
        <>
          {/* Header với back button và lesson name */}
          <View style={styles.headerContainer}>
            <AppButton
              prefixIcon={'outline/user interface/e-remove'}
              prefixIconSize={20}
              backgroundColor={ColorThemes.light.transparent}
              textColor={ColorThemes.light.Neutral_Text_Color_Title}
              borderColor="transparent"
              containerStyle={styles.closeButton}
              onPress={navigateBack}
            />
            <View style={styles.headerTitleContainer}>
              <Text style={styles.headerTitle} numberOfLines={1}>
                {t('course.lesson', {stepOrder: Step.stepOrder, lessonName: lessonData?.Name ?? ''})}
              </Text>
            </View>
          </View>
        </>
      )}
      <TabView
        navigationState={{index: tabIndex, routes: tabRoutes}}
        renderScene={renderTabScene}
        onIndexChange={setTabIndex}
        renderTabBar={renderTabBar}
        style={styles.tabView}
        swipeEnabled={false}
      />
    </View>
  );


}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    zIndex: 111,
    position: 'absolute',
  },
  loadingContainer: {
    height: 200,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    justifyContent: 'center',
    alignItems: 'center',
  },

  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.Error_Color_Main,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 16,
  },
  closeButton: {
    paddingHorizontal: 12,
    borderRadius: 100,
    width: 40,
    height: 40,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
    zIndex: 111,
  },
  headerTitleContainer: {
    flex: 1,
    marginLeft: 12,
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontSize: 16,
    fontWeight: '600',
  },

  placeholderContainer: {
    height: 200,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  noVideoText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginTop: 8,
  },
  tabIndicator: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    height: 1.5,
  },
  tabStyle: {
    paddingHorizontal: 4,
    paddingTop: 0,
    alignItems: 'center',
  },
  tabBar: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    height: 45,
    elevation: 0,
  },
  mainContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
  tabView: {
    flex: 1,
  },
  videoTabContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  fixedVideoSection: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  videoPlayerContainer: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  scrollableDocumentSection: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  // Video Navigation Bar Styles
  videoNavigationContainer: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  videoNavigationContent: {
    alignItems: 'center',
    gap: 12,
  },
  videoNavButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderWidth: 2,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
    position: 'relative',
  },
  videoNavButtonActive: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    borderColor: ColorThemes.light.Primary_Color_Main,
  },
  videoNavButtonCompleted: {
    backgroundColor: ColorThemes.light.Success_Color_Background_color,
    borderColor: ColorThemes.light.Success_Color_Main,
  },
  videoNavButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  videoNavButtonTextActive: {
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  videoNavButtonTextCompleted: {
    color: ColorThemes.light.Success_Color_Main,
  },
  completedIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: ColorThemes.light.Success_Color_Main,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Document Styles
  floatingButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    zIndex: 1000,
    bottom: 20,
    right: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  pdfViewContainer: {
    flex: 1,
    width: '100%',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 100,
  },
  documentListContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  documentListContent: {
    paddingHorizontal: 16,
    gap: 16,
    paddingTop: 16,
  },
  documentItem: {
    borderRadius: 8,
    marginBottom: 8,
    overflow: 'hidden',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  documentItemActive: {
    backgroundColor: ColorThemes.light.primary_light_color,
    borderWidth: 2,
    borderColor: ColorThemes.light.Primary_Color_Main,
  },
  documentIconContainer: {
    position: 'relative',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.primary_light_color,
  },
  documentIconContainerActive: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
  },
  currentIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ColorThemes.light.Success_Color_Main,
  },
  documentLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    minHeight: 200,
  },
  loadingText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginTop: 8,
  },

  // Video Navigation with Download Button Styles
  videoNavigationWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  currentVideoDownloadContainer: {
    marginLeft: 12,
  },
});



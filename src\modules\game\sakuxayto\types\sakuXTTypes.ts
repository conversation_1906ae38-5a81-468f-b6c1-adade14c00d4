// TypeScript interfaces cho SakuXT Game

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T[];
  total?: number;
}

// Data từ bảng GameConfig
export interface SakuXTGameConfigAPI {
  Id: string;
  GameId: string;
  Score: number;        // Điểm trên mỗi mạng
  LifeCount: number;    // Số mạng chơi
  Time: number;         // Thời gian chơi (giây)
  Bonus: number;        // Điểm bonus khi hoàn thành không mất mạng
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
  ScoreHint?: number
}

// Data từ bảng GameQuestion
export interface SakuXTGameQuestionAPI {
  Id: string;
  GameId: string;
  Stage: number;
  Name: string;         // Nội dung câu hỏi (VD: "🔊 Tôi ăn sáng lúc 7 giờ")
  Audio?: string;       // File âm thanh (optional)
  Purpose: string;      // CompetenceId
  Sort: number;
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
  Suggest?: string;     // Nội dung gợi ý (hint)
}

// Data từ bảng GameAnswer
export interface SakuXTGameAnswerAPI {
  Id: string;
  GameQuestionId: string;
  Name: string;         // Từ/cụm từ (VD: "I", "eat", "breakfast")
  Sort: number;         // Thứ tự đúng trong câu (1, 2, 3...)
  IsCorrect: boolean;   // Để check đáp án đúng/sai
  IsActive: boolean;
  CreatedDate?: string;
  UpdatedDate?: string;
  IsResult?: boolean;
}

// Transformed interfaces for game use
export interface SakuXTGameConfig {
  gameId: string;
  scorePerLife: number;
  maxLives: number;
  timeLimit: number;
  bonusScore: number;
  gemHint: number;
  isActive: boolean;
}

export interface SakuXTQuestion {
  id: string;
  questionText: string;
  audioUrl?: string;
  words: SakuXTWord[];
  stage: number;
  competenceId: string;
  // add more fields here
  Suggest?: string;
}

export interface SakuXTWord {
  id: string;
  text: string;
  correctPosition: number;  // Sort field từ API
  currentPosition?: number; // Vị trí hiện tại trong drop zone
  IsResult?: boolean;
}

// Game state interface
export interface SakuXTGameState {
  // API Data
  questions: SakuXTQuestion[];
  gameConfig: SakuXTGameConfig | null;

  // Current Question
  currentQuestionIndex: number;
  currentQuestion: SakuXTQuestion | null;
  availableWords: SakuXTWord[];

  // Game State
  wordsInCheck: SakuXTWord[];
  wordsInDropZone: SakuXTWord[];
  wordsOutSideZone: SakuXTWord[];
  isAnswerCorrect: boolean | null;
  feedbackMessage: string;
  feedbackType: 'success' | 'error' | null;

  // Progress
  questionDone: number;
  totalQuestion: number;
  currentStage: number;

  // Scoring & Config (from GameConfig API)
  maxLives: number;
  currentLives: number;
  timeLimit: number;
  timeRemaining: number;
  scorePerLife: number;
  bonusScore: number;
  currentScore: number;

  // API State
  loading: boolean;
  configLoading: boolean;
  error: string | null;
  configError: string | null;
  initialized: boolean;
  configInitialized: boolean;
}



import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Text,
  Dimensions,
  StatusBar,
  Platform,
  Animated,
} from 'react-native';
import Video from 'react-native-video';
import Orientation from 'react-native-orientation-locker';
import {Winicon} from 'wini-mobile-components';
import Slider from '@react-native-community/slider';
import {ColorThemes} from '../../../assets/skin/colors';

interface VideoPlayerFullscreenProps {
  source: string;
  onProgressPercent?: (percent: number) => void;
  onClose: () => void;
  initialPosition?: number;
}

const VideoPlayerFullscreen: React.FC<VideoPlayerFullscreenProps> = ({
  source,
  onProgressPercent,
  onClose,
  initialPosition = 0,
}) => {
  const videoRef = useRef<any>(null);
  const [paused, setPaused] = useState(true); // Start paused to disable auto play
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(initialPosition);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isSliding, setIsSliding] = useState(false);
  const [controlsVisible, setControlsVisible] = useState(true);
  const {width, height} = Dimensions.get('window');
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Toggle controls visibility with fade animation
  const toggleControls = () => {
    const newVisibility = !controlsVisible;
    setControlsVisible(newVisibility);

    Animated.timing(fadeAnim, {
      toValue: newVisibility ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();

    // Auto-hide controls after 3 seconds if showing
    if (newVisibility) {
      resetHideTimer();
    }
  };

  // Reset the auto-hide timer
  const resetHideTimer = () => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }
    hideTimeoutRef.current = setTimeout(() => {
      if (controlsVisible) {
        setControlsVisible(false);
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }
    }, 3000);
  };

  // Đặt orientation sang landscape khi component mount
  useEffect(() => {
    // Lưu orientation hiện tại
    const initialOrientation = Orientation.getInitialOrientation();

    // Chuyển sang landscape
    Orientation.lockToLandscape();

    // Hiển thị thông báo hướng dẫn đóng fullscreen
    const timeout = setTimeout(() => {
      // Hiển thị thông báo trong 3 giây
      setShowCloseHint(true);
      setTimeout(() => {
        setShowCloseHint(false);
      }, 3000);
    }, 500);

    // Start auto-hide timer for controls
    resetHideTimer();

    // Reset về portrait khi unmount
    return () => {
      clearTimeout(timeout);
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
      Orientation.lockToPortrait();
    };
  }, []);

  // State để hiển thị gợi ý đóng
  const [showCloseHint, setShowCloseHint] = useState(false);

  const handleLoad = (data: any) => {
    setDuration(data.duration);
    setIsLoading(false);
    // Seek đến vị trí ban đầu nếu có
    if (initialPosition > 0 && videoRef.current) {
      videoRef.current.seek(initialPosition);
    }
  };

  const handleError = (error: any) => {
    console.error('Video error:', error);
    setHasError(true);
    setIsLoading(false);
  };

  const handleProgress = (data: any) => {
    if (!isSliding) {
      const percent = duration > 0 ? (data.currentTime / duration) * 100 : 0;
      setCurrentTime(data.currentTime);

      if (onProgressPercent) {
        onProgressPercent(percent);
      }
    }
  };

  const handleSeek = (seconds: number) => {
    const newTime = Math.max(0, Math.min(currentTime + seconds, duration));
    videoRef.current?.seek(newTime);
    setCurrentTime(newTime);
  };

  const onSlidingStart = () => {
    setIsSliding(true);
    resetHideTimer();
  };

  const onSlidingComplete = (value: number) => {
    videoRef.current?.seek(value);
    setCurrentTime(value);
    setIsSliding(false);
    resetHideTimer();
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <Modal
      visible={true}
      animationType="fade"
      supportedOrientations={['landscape']}
      onRequestClose={handleClose}>
      <StatusBar hidden={true} />
      <View style={styles.container}>
        {/* Nút đóng ở góc trên bên phải */}
        <Animated.View style={[styles.closeButtonTop, {opacity: fadeAnim}]}>
          <TouchableOpacity onPress={handleClose}>
            <Winicon src="fill/arrows/fullscreen" size={24} color="#fff" />
          </TouchableOpacity>
        </Animated.View>

        {/* Gợi ý đóng fullscreen */}
        {showCloseHint && (
          <Animated.View style={[styles.closeHint, {opacity: fadeAnim}]}>
            <Text style={styles.closeHintText}>
              Nhấn nút ở góc trên bên phải để thoát chế độ toàn màn hình
            </Text>
          </Animated.View>
        )}

        {isLoading && (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading video...</Text>
          </View>
        )}

        {hasError ? (
          <View style={styles.errorContainer}>
            <Winicon
              src="outline/user interface/alert-triangle"
              size={32}
              color="#FF0000"
            />
            <Text style={styles.errorText}>Failed to load video</Text>
            <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.videoWrapper}>
            <TouchableOpacity
              style={StyleSheet.absoluteFill}
              activeOpacity={1}
              onPress={toggleControls}>
              <Video
                ref={videoRef}
                source={{uri: source}}
                style={StyleSheet.absoluteFill}
                paused={paused}
                resizeMode="contain"
                onLoad={handleLoad}
                onProgress={handleProgress}
                onError={handleError}
              />
            </TouchableOpacity>

            {/* Controls */}
            <Animated.View style={[styles.controls, {opacity: fadeAnim}]}>
              <TouchableOpacity
                onPress={() => {
                  handleSeek(-10);
                  resetHideTimer();
                }}
                style={styles.controlBtn}>
                <Winicon
                  src="outline/arrows/ctrl-backward"
                  size={24}
                  color="#fff"
                />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  setPaused(!paused);
                  resetHideTimer();
                }}
                style={styles.controlBtn}>
                <Winicon
                  src={
                    paused
                      ? 'color/multimedia/btn-play-2'
                      : 'color/multimedia/button-pause'
                  }
                  size={24}
                  color="#fff"
                />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  handleSeek(10);
                  resetHideTimer();
                }}
                style={styles.controlBtn}>
                <Winicon
                  src="outline/arrows/ctrl-forward"
                  size={24}
                  color="#fff"
                />
              </TouchableOpacity>

              <Text style={styles.timeText}>
                {formatTime(currentTime)} / {formatTime(duration)}
              </Text>
            </Animated.View>

            {/* Slider */}
            <Animated.View
              style={[styles.sliderContainer, {opacity: fadeAnim}]}>
              <Slider
                style={styles.slider}
                minimumValue={0}
                maximumValue={duration}
                value={currentTime}
                onSlidingStart={onSlidingStart}
                onSlidingComplete={onSlidingComplete}
                minimumTrackTintColor="#FF0000"
                maximumTrackTintColor="#888"
                thumbTintColor="#FFF"
              />
            </Animated.View>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  videoWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controls: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  controlBtn: {
    padding: 4,
    marginHorizontal: 10,
  },
  timeText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 20,
  },
  sliderContainer: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  loadingText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  errorText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 10,
    marginBottom: 20,
  },
  closeButton: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
    marginTop: 20,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  closeButtonTop: {
    position: 'absolute',
    bottom: 55,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 30,
    padding: 10,
    zIndex: 1000,
  },
  closeHint: {
    position: 'absolute',
    top: 70,
    right: 20,
    left: 20,
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 10,
    padding: 15,
    zIndex: 1000,
    alignItems: 'center',
  },
  closeHintText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default VideoPlayerFullscreen;

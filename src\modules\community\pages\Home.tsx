/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  StatusBar,
  RefreshControl,
} from 'react-native';
import {FBottomSheet, FDialog, ListTile, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {SkeletonPlacePostCard} from '../card/defaultPost';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {useDispatch, useSelector} from 'react-redux';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import {ProfileView} from './Chat';
import {useTranslation} from 'react-i18next';
import {LogoImg} from '../../../Screen/Page/Home';
import {TabBar, PostItem} from '../modules/Home';

export default function Home() {
  const {t} = useTranslation();
  const user = useSelectorCustomerState().data;
  const [activeTab, setActiveTab] = React.useState(0);
  const bottomSheetRef = useRef<any>(null);

  const dialogRef = React.useRef<any>(null);
  const flatListRef = useRef<any>(null);
  const dispatch: AppDispatch = useDispatch();
  const navigation = useNavigation<any>();
  const size = 50;
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true); // Thêm state để kiểm tra còn data không

  const data = useSelector((state: RootState) => state.newsFeed.data);
  const {loading} = useSelector((state: RootState) => state.newsFeed);

  useEffect(() => {
    refreshData();
  }, [activeTab]);

  const handleRefresh = async () => {
    if (!loading) {
      // Thêm check này để tránh gọi refresh khi đang loading
      setIsRefreshing(true);
      setHasMore(true);
      try {
        refreshData();
      } catch (error) {
        console.error('Refresh error:', error);
      } finally {
      }
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  };

  const handleLoadMore = async () => {
    // Clear any existing timeout to prevent multiple calls
  };

  const refreshData = () => {
    if (activeTab === 0) {
      dispatch(newsFeedActions.getNewFeedPopular(1, size));
    } else if (activeTab === 1) {
      dispatch(newsFeedActions.getNewFeed(1, size));
    } else if (activeTab === 2) {
      dispatch(newsFeedActions.getNewFeedFollowing(1, size));
    } else if (activeTab === 3) {
      dispatch(newsFeedActions.getNewFeedSaved(1, size));
    }
  };
  const getItemLayout = React.useCallback(
    (data: any, index: number) => ({
      length: 300, // Approximate height of each item
      offset: 300 * index,
      index,
    }),
    [],
  );

  return (
    <View style={styles.container}>
      <FDialog ref={dialogRef} />
      <FBottomSheet ref={bottomSheetRef} />

      <StatusBar
        barStyle="dark-content"
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Absolute}
      />
      {/* header */}
      <ListTile
        style={styles.listTile}
        isClickLeading
        leading={
          <TouchableOpacity
            onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
            style={styles.leadingButton}>
            <LogoImg />
          </TouchableOpacity>
        }
        title={t('community.tabs.home')}
        trailing={
          <View style={styles.trailingContainer}>
            <TouchableOpacity
              style={styles.plusButton}
              onPress={() => {
                if (!user) {
                  dialogCheckAcc(dialogRef);
                  return;
                }
                navigation.push(RootScreen.createPost, {groupId: null});
              }}>
              <Winicon
                src="outline/layout/plus"
                size={20}
                color={ColorThemes.light.Neutral_Text_Color_Title}
              />
            </TouchableOpacity>
            <ProfileView />
          </View>
        }
      />
      {/*  */}
      <TabBar
        activeTab={activeTab}
        onTabPress={setActiveTab}
        flatListRef={flatListRef}
      />

      <FlatList
        ref={flatListRef}
        data={data}
        renderItem={({item}) => (
          <PostItem
            item={item}
            user={user}
            dialogRef={dialogRef}
            bottomSheetRef={bottomSheetRef}
          />
        )}
        keyExtractor={item => item?.Id.toString()}
        getItemLayout={getItemLayout}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        windowSize={10}
        initialNumToRender={5}
        showsVerticalScrollIndicator={false}
        style={styles.flatList}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        contentContainerStyle={[
          styles.flatListContent,
          {
            gap: data.length === 0 ? 0 : 8,
          },
        ]}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={() => {
          if (loading && data.length === 0) {
            return (
              <View style={styles.listEmptyLoadingContainer}>
                {[1, 2, 3].map((_, index) => (
                  <SkeletonPlacePostCard key={`skeleton-${index}`} />
                ))}
              </View>
            );
          } else {
            return (
              <View style={styles.listEmptyContainer}>
                <EmptyPage title="Không có dữ liệu" />
              </View>
            );
          }
        }}
        ListFooterComponent={() => {
          if (loading && !isRefreshing) {
            return (
              <View style={styles.listFooterContainer}>
                <SkeletonPlacePostCard />
              </View>
            );
          }
          if (!hasMore && data?.length > 0) {
            return <EmptyPage title="Không còn dữ liệu" />;
          }
          return null;
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  listTile: {
    padding: 0,
    paddingBottom: 8,
    paddingHorizontal: 16,
  },
  leadingButton: {
    padding: 4,
  },
  trailingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  plusButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  flatList: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  flatListContent: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  listEmptyLoadingContainer: {
    gap: 8,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  listEmptyContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  listFooterContainer: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
});

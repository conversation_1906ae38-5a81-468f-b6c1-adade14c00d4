import {DataController} from '../../base/baseController';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../utils/AsyncStorage';
import {StorageContanst} from '../../Config/Contanst';
import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {CourseDA} from '../../modules/Course/da';
const FETCH_COURSE_RECENT = 'FETCH_COURSE_RECENT';
const ADD_COURSE_RECENT = 'ADD_COURSE_RECENT';
const UPDATE_LIKE = 'UPDATE_LIKE';
const initialState: {
  recentData: any[];
  loading: boolean;
  success: boolean;
  error: any; // Hoặc để null|string nếu muốn cụ thể hơn
} = {
  recentData: [],
  loading: true,
  success: false,
  error: null,
};
export const courseSlice = createSlice({
  name: 'course',
  initialState: initialState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case FETCH_COURSE_RECENT:
          state.recentData = action.payload.data;
          break;
        case ADD_COURSE_RECENT:
          const updatedList = [...state.recentData, action.payload.data];
          state.recentData = updatedList;
          break;
        case UPDATE_LIKE:
          state.recentData = [
            ...state.recentData.map((item: any) => {
              if (item.Id === action.payload.id) {
                // Cập nhật thông tin likes
                return {
                  ...item,
                  IsLike: action.payload.IsLike,
                  //   likedByCurrentUser: action.payload.likeData.liked,
                };
              }
              return item;
            }),
          ];
          break;
        default:
          break;
      }
      state.loading = false;
    },
    onFetching: state => {
      state.loading = true;
    },
    // onReset: (state) => {

    // }
  },
});
export default courseSlice.reducer;
const {handleActions, onFetching} = courseSlice.actions;
const courseDA = new CourseDA();
export class CourseActions {
  static getCourseRecent = () => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    var listId = await getDataToAsyncStorage(StorageContanst.RecentCourse);
    if (listId != null) {
      const lst = listId.split(',');
      const controller = new DataController('Course');
      const result = await controller.getListSimple({
        query: `@Id: {${lst.join(' | ')}}`,
        returns: ['Id', 'Name', 'Price', 'Img'],
      });
      if (result.code === 200) {
        const lst = await Promise.all(
          result.data.map(async (item: any) => {
            return {
              ...item,
              IsLike: await courseDA.checkCourseIsWishlishCustomer(item.Id),
            };
          }),
        );
        dispatch(
          handleActions({
            type: FETCH_COURSE_RECENT,
            data: lst,
          }),
        );
      }
    }
  };
  static addRecent = (courseItem: any) => async (dispatch: Dispatch) => {
    var lst = [];
    var listId = await getDataToAsyncStorage(StorageContanst.RecentCourse);
    if (listId) {
      lst = listId.split(',');
      if (!lst.some(item => item === courseItem.Id)) {
        lst.push(courseItem.Id);
        await saveDataToAsyncStorage(
          StorageContanst.RecentCourse,
          lst.join(','),
        );
        dispatch(
          handleActions({
            type: ADD_COURSE_RECENT,
            data: {
              Id: courseItem.Id,
              Name: courseItem.Name,
              Price: courseItem.Price,
              Img: courseItem.Img,
              IsLike: courseItem.IsLike,
            },
          }),
        );
      }
    } else {
      await saveDataToAsyncStorage(StorageContanst.RecentCourse, courseItem.Id);
      dispatch(
        handleActions({
          type: ADD_COURSE_RECENT,
          data: {
            Id: courseItem.Id,
            Name: courseItem.Name,
            Price: courseItem.Price,
            Img: courseItem.Img,
            IsLike: courseItem.IsLike,
          },
        }),
      );
    }
  };
  static updateWishlistCourse =
    (id: string, isRemove: boolean) => async (dispatch: Dispatch) => {
      if (isRemove) {
        const result = await courseDA.deleteWishlistCourse(id);
        if (result) {
          dispatch(
            handleActions({
              type: UPDATE_LIKE,
              id: id,
              IsLike: false,
            }),
          );
        }
      } else {
        const result = await courseDA.addWishlistCourse(id);
        if (result) {
          dispatch(
            handleActions({
              type: UPDATE_LIKE,
              id: id,
              IsLike: true,
            }),
          );
        }
      }
    };
}

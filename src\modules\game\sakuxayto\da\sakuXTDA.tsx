import {DataController} from '../../../../base/baseController';
import {
  ApiResponse,
  SakuXTGameAnswerAPI,
  SakuXTGameConfig,
  SakuXTGameConfigAPI,
  SakuXTGameQuestionAPI,
  SakuXTQuestion,
  SakuXTWord,
} from '../types/sakuXTTypes';

export class SakuXTDA {
  private questionController: DataController;
  private answerController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuestion');
    this.answerController = new DataController('GameAnswer');
  }

  /**
   * <PERSON><PERSON><PERSON> cấu hình game từ bảng GameConfig
   * @param gameId ID của game SakuXT
   * @returns Promise<SakuXTGameConfig>
   */
  static async getGameConfig(gameId: string): Promise<SakuXTGameConfig> {
    try {
      console.log(`[SakuXTDA] Loading game config for GameId: ${gameId}`);

      const controller = new DataController('GameConfig');
      const response: ApiResponse<SakuXTGameConfigAPI> =
        await controller.getListSimple({
          query: `@GameId: {${gameId}}`,
        });
      if (
        response.code !== 200 ||
        !response.data ||
        response.data.length === 0
      ) {
        throw new Error(
          'No game config found or API returned unsuccessful response',
        );
      }

      const configData = response.data[0];
      const transformedConfig: SakuXTGameConfig = {
        gameId: gameId,
        scorePerLife: configData.Score || 10,
        maxLives: configData.LifeCount || 3,
        timeLimit: configData.Time || 300,
        bonusScore: configData.Bonus || 50,
        isActive: configData.IsActive || true,
        gemHint: configData.ScoreHint || 0,
      };
      console.log(
        '[SakuXTDA] Successfully loaded game config:',
        transformedConfig,
      );
      return transformedConfig;
    } catch (error) {
      console.error('[SakuXTDA] Error loading game config:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách câu hỏi từ bảng GameQuestion
   * @param gameId ID của game
   * @param stage Stage của game
   * @param competenceId ID competence
   * @returns Promise<SakuXTGameQuestionAPI[]>
   */
  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number,
    competenceId: string,
  ): Promise<SakuXTGameQuestionAPI[]> {
    try {
      console.log(
        `[SakuXTDA] Loading questions for GameId: ${gameId}, Stage: ${stage}, CompetenceId: ${competenceId}`,
      );

      const response: ApiResponse<SakuXTGameQuestionAPI> =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}]`,
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        });
      if (response.code !== 200) {
        throw new Error(`API returned error code: ${response.code}`);
      }
      //lấy danh sách answer
      const answerResponse = await this.answerController.getListSimple({
        query: `@GameQuestionId: {${response.data
          .map((q: any) => q.Id)
          .join(' | ')}}`,
      });
      if (
        answerResponse &&
        answerResponse.data &&
        answerResponse.data.length > 0
      ) {
        // Map các câu trả lời vào câu hỏi
        response.data.forEach((question: any) => {
          question.Answers = answerResponse.data.filter(
            (answer: any) => answer.GameQuestionId === question.Id,
          );
        });
      }
      const questions = response.data || [];
      return questions;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Transform raw API data thành format phù hợp cho game
   * @param rawQuestions Dữ liệu thô từ API (đã có Answers property)
   * @returns SakuXTQuestion[]
   */
  static transformQuestionsWithAnswers(
    rawQuestions: SakuXTGameQuestionAPI[],
  ): SakuXTQuestion[] {
    return rawQuestions.map(question => {
      // Lấy answers từ question.Answers (đã được load trong getQuestionsByGameAndStage)
      const questionAnswers = (question as any).Answers || [];

      // Transform answers thành words
      const words: SakuXTWord[] = questionAnswers.map(
        (answer: SakuXTGameAnswerAPI) => ({
          id: answer.Id,
          text: answer.Name,
          correctPosition: answer.Sort,
          IsResult: answer.IsResult ?? false,
        }),
      );
      return {
        id: question.Id,
        questionText: question.Name,
        audioUrl: question.Audio,
        words: words,
        stage: question.Stage,
        competenceId: question.Purpose,
        Suggest: question.Suggest,
      };
    });
  }
}

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { StartVCNV } from '../StartVCNV';

// Test component để test VCNV game với API data
const VCNVTestScreen = () => {
  const [testParams, setTestParams] = React.useState({
    gameId: 'VCNV_TEST',
    stage: 1,
    competenceId: 'COMP_001'
  });

  const [showGame, setShowGame] = React.useState(false);

  const testConfigs = [
    {
      name: 'Test Config 1',
      gameId: 'VCNV_GAME_1',
      stage: 1,
      competenceId: 'COMP_001'
    },
    {
      name: 'Test Config 2', 
      gameId: 'VCNV_GAME_2',
      stage: 2,
      competenceId: 'COMP_002'
    },
    {
      name: 'Test Config 3',
      gameId: 'VCNV_GAME_3',
      stage: 1,
      competenceId: 'COMP_003'
    }
  ];

  if (showGame) {
    return (
      <StartVCNV 
        gameId={testParams.gameId}
        stage={testParams.stage}
        competenceId={testParams.competenceId}
      />
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>VCNV Game Test</Text>
      <Text style={styles.subtitle}>Chọn cấu hình test:</Text>
      
      {testConfigs.map((config, index) => (
        <TouchableOpacity
          key={index}
          style={styles.testButton}
          onPress={() => {
            setTestParams(config);
            setShowGame(true);
          }}
        >
          <Text style={styles.testButtonText}>{config.name}</Text>
          <Text style={styles.testButtonSubtext}>
            GameId: {config.gameId} | Stage: {config.stage} | CompetenceId: {config.competenceId}
          </Text>
        </TouchableOpacity>
      ))}

      <TouchableOpacity
        style={[styles.testButton, styles.customButton]}
        onPress={() => setShowGame(true)}
      >
        <Text style={styles.testButtonText}>Test với params hiện tại</Text>
        <Text style={styles.testButtonSubtext}>
          GameId: {testParams.gameId} | Stage: {testParams.stage} | CompetenceId: {testParams.competenceId}
        </Text>
      </TouchableOpacity>

      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Thông tin test:</Text>
        <Text style={styles.infoText}>• Game sẽ load data từ API</Text>
        <Text style={styles.infoText}>• Nếu API fail sẽ hiển thị error message</Text>
        <Text style={styles.infoText}>• Có thể fallback về data cũ nếu cần</Text>
        <Text style={styles.infoText}>• Kiểm tra splitJapanese API</Text>
        <Text style={styles.infoText}>• Test logic tạo crossword grid</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#FFC670',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#112164',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#112164',
    marginBottom: 20,
  },
  testButton: {
    backgroundColor: '#112164',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  customButton: {
    backgroundColor: '#1BDB55',
  },
  testButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  testButtonSubtext: {
    color: '#E8E8E8',
    fontSize: 12,
    marginTop: 5,
  },
  infoContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 8,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#112164',
    marginBottom: 5,
  },
});

export default VCNVTestScreen;

# iOS-Style Switch Component

A customizable iOS-style switch component for React Native with smooth animations.

## Features

- Smooth animations when toggling the switch
- Customizable colors for track and thumb
- Adjustable size
- Support for disabled state
- Returns boolean value when toggled

## Usage

```jsx
import React, { useState } from 'react';
import { View, Text } from 'react-native';
import IOSSwitch from './components/IOSSwitch';

const MyComponent = () => {
  const [isEnabled, setIsEnabled] = useState(false);

  return (
    <View style={{ padding: 20 }}>
      <Text>Toggle Switch:</Text>
      <IOSSwitch 
        value={isEnabled} 
        onValueChange={setIsEnabled} 
      />
      <Text>Switch is {isEnabled ? 'ON' : 'OFF'}</Text>
    </View>
  );
};

export default MyComponent;
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| value | boolean | required | The current state of the switch |
| onValueChange | function | required | Callback function when the switch is toggled |
| onColor | string | '#34C759' | Color of the track when switch is ON |
| offColor | string | '#E9E9EA' | Color of the track when switch is OFF |
| thumbColor | string | '#FFFFFF' | Color of the switch thumb/knob |
| size | number | 1 | Size multiplier (1 = default iOS size) |
| disabled | boolean | false | Whether the switch is disabled |
| style | ViewStyle | undefined | Additional styles for the container |

## Customization Examples

### Custom Colors

```jsx
<IOSSwitch 
  value={isEnabled} 
  onValueChange={setIsEnabled}
  onColor="#3AAC6D"  // Green color when ON
  offColor="#CCCCCC" // Gray color when OFF
  thumbColor="#FFFFFF"
/>
```

### Custom Size

```jsx
<IOSSwitch 
  value={isEnabled} 
  onValueChange={setIsEnabled}
  size={1.2} // 20% larger than default
/>
```

### Disabled State

```jsx
<IOSSwitch 
  value={isEnabled} 
  onValueChange={setIsEnabled}
  disabled={true}
/>
```

## Implementation Details

The component uses React Native's Animated API to create smooth transitions between states. It includes:

- Spring animations for the thumb movement
- Timing animations for color transitions
- Scale animations when pressing the switch

The switch is built with accessibility in mind and follows iOS design guidelines for toggle switches.

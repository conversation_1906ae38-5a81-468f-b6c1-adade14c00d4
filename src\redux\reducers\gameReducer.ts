import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {getCurrentScore} from '../../modules/game/gameAsyncThunk';

interface GameState {
  gem: number;
  gemCost: number;
  cup: number;
  totalLives: number;
  currentLives: number;
  isGameOver: boolean;
  time: number;
  messageGameOver: string;
  isRunTime: boolean;
  gemUse: number;
  gemAdd: number;
}

const initialState: GameState = {
  gem: 0,
  cup: 0,
  totalLives: 3,
  currentLives: 3,
  isGameOver: false,
  time: 300,
  messageGameOver: 'Rất tiếc!',
  isRunTime: false,
  gemUse: 10,
  gemAdd: 10,
  gemCost: 10,
};

export const GameSlice = createSlice({
  name: 'Game',
  initialState,
  reducers: {
    setData(state, action) {
      const {stateName, value} = action.payload;
      (state as any)[stateName] = value;
    },
    restartGame: (state: GameState) => {
      state.currentLives = initialState.currentLives;
      state.isGameOver = false;
      state.time = initialState.time;
      state.isRunTime = true;
    },
    gameOver: (state: GameState, action: PayloadAction<string>) => {
      state.messageGameOver = action.payload;
      state.isGameOver = true;
      state.isRunTime = false;
    },
    reset: (state: GameState) => {
      state.currentLives = initialState.currentLives;
      state.isGameOver = false;
      state.time = initialState.time;
      state.isRunTime = true;
    },
  },
  extraReducers: builder => {
    builder.addCase(getCurrentScore.fulfilled, (state, action) => {
      state.gem = action.payload;
    });
  },
});

export const {setData, reset, restartGame, gameOver} = GameSlice.actions;

export default GameSlice.reducer;

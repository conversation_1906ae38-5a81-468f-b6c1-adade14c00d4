import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
  Pressable,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { GameDA } from '../gameDA';
import { useSelectorCustomerState } from '../../../redux/hook/customerHook';
import StaticEnum from '../../../Config/StaticEnum';

const {width} = Dimensions.get('window');

interface ChooseLevelModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectLevel: (level: any) => void;
  selectedLevel?: string;
  unlockedLevels?: string[];
  gameId: string;
}

const ChooseLevelModal: React.FC<ChooseLevelModalProps> = ({
  visible,
  onClose,
  onSelectLevel,
  unlockedLevels = [],
  selectedLevel = 'N5',
  gameId,
}) => {
  const [allLevels, setAllLevels] = useState<any[]>([]);
  // const [unlockedLevels, setUnlockedLevels] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const customerState = useSelectorCustomerState();

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  // Animation effect for show/hide modal
  useEffect(() => {
    if (visible) {
      // Show animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Hide animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, fadeAnim, scaleAnim]);

  useEffect(() => {

    const fetchCompetenceData = async () => {
      try {
        setLoading(true);

        // Lấy danh sách tất cả trình độ của game
        const competence = StaticEnum.GameCompetencybyGame.find((comp: any) => comp.gameId === gameId);
        if (competence && competence.data) {
          setAllLevels(competence.data);
        }
      } catch (error) {
        console.error('Lỗi khi lấy dữ liệu trình độ:', error);
        // Fallback data nếu có lỗi
        setAllLevels([]);
      } finally {
        setLoading(false);
      }
    };

    if (visible && gameId) {
      fetchCompetenceData();
    }
  }, [visible, gameId, customerState.data?.Id]);

  const handleLevelPress = (level: any) => {
    if (unlockedLevels.includes(level.Name) && level.IsActive) {
      onSelectLevel(level);
      onClose();
    }
  };

  const renderLevelButton = (level: any) => {
    const isSelected = selectedLevel === level.name;
    const isUnlocked = unlockedLevels.includes(level.name) && level.IsActive;

    const buttonStyle = [
      styles.levelButton,
      isSelected && styles.selectedLevelButton,
      !isUnlocked && styles.lockedLevelButton,
    ];

    const textStyle = [
      styles.levelButtonText,
      isSelected && styles.selectedLevelButtonText,
      !isUnlocked && styles.lockedLevelButtonText,
    ];

    return (
      <TouchableOpacity
        key={level.Id}
        style={buttonStyle}
        onPress={() => handleLevelPress(level)}
        disabled={!isUnlocked}
        activeOpacity={isUnlocked ? 0.7 : 1}>
        <Text style={textStyle}>{level.name}</Text>
      </TouchableOpacity>
    );
  };

  if (!visible) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.modalOverlay,
        {
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 10000,
          opacity: fadeAnim,
        }
      ]}>
      <Pressable onPress={onClose} style={styles.modalOverlay}>
        <Animated.View
          style={[
            styles.modalContainer,
            {
              transform: [{ scale: scaleAnim }],
            }
          ]}>
          {/* Header */}
          <View style={styles.headerContainer}>
            <Text style={styles.headerText}>TRÌNH ĐỘ</Text>
          </View>

          {/* Description */}
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionText}>
              Bạn chỉ chọn được các trình độ đã phá đảo
            </Text>
          </View>

          {/* Level Buttons */}
          <View style={styles.levelsContainer}>
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#112164" />
                <Text style={styles.loadingText}>Đang tải trình độ...</Text>
              </View>
            ) : (
              allLevels.map(level => renderLevelButton(level))
            )}
          </View>

          {/* Close button area - tap outside to close */}
          <TouchableOpacity
            style={styles.closeArea}
            onPress={onClose}
            activeOpacity={1}
          />
        </Animated.View>
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: width * 0.85,
    backgroundColor: '#D4B896', // Màu nền be/nâu nhạt như trong hình
    borderRadius: 20,
    paddingVertical: 30,
    paddingHorizontal: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContainer: {
    marginBottom: 20,
  },
  headerText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#112164', // Màu xanh đậm
    textAlign: 'center',
    letterSpacing: 2,
  },
  descriptionContainer: {
    marginBottom: 30,
    paddingHorizontal: 10,
  },
  descriptionText: {
    fontSize: 16,
    color: '#112164', // Màu xanh đậm
    textAlign: 'center',
    lineHeight: 22,
  },
  levelsContainer: {
    width: '100%',
    alignItems: 'center',
  },
  levelButton: {
    width: '80%',
    height: 50,
    backgroundColor: '#E8E8E8', // Màu xám nhạt cho button mặc định
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 8,
    borderWidth: 2,
    borderColor: '#D0D0D0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedLevelButton: {
    backgroundColor: '#4CAF50', // Màu xanh lá cây cho level được chọn
    borderColor: '#45A049',
  },
  lockedLevelButton: {
    backgroundColor: '#F0F0F0',
    borderColor: '#E0E0E0',
    opacity: 0.6,
  },
  levelButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
  },
  selectedLevelButtonText: {
    color: '#FFFFFF',
  },
  lockedLevelButtonText: {
    color: '#999999',
  },
  closeArea: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  loadingContainer: {
    paddingVertical: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    color: '#112164',
    textAlign: 'center',
  },
});

export default ChooseLevelModal;

import React from 'react';
import {useRoute} from '@react-navigation/native';
import GenericGameHomeScreen from '../config/GameConfig';
import {getGameConfig} from '../config/GameConfig';

// DHBC Home Screen using Generic System
const DHBCHomeScreen: React.FC = () => {
  const route = useRoute<any>();
  const {gameId} = route.params || {gameId: 'DHBC'};

  // Get game configuration
  const gameConfig = getGameConfig(gameId);

  return (
    <GenericGameHomeScreen
      gameId={gameId}
      // Có thể override components nếu cần
      // HeaderComponent={CustomDHBCHeader}
      // FooterComponent={CustomDHBCFooter}
      // MilestoneComponent={CustomDHBCMilestone}
      // StartGameModalComponent={CustomDHBCModal}
    />
  );
};

export default DHBCHomeScreen;

import React, {useRef} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Animated} from 'react-native';
import * as Animatable from 'react-native-animatable';

interface QuestionProps {
  question: {text: string; options: string[]; correctAnswer: string};
  onAnswer: (answer: string) => void;
  selectedAnswer: string | null;
  isCorrect: boolean | null;
}

const Question: React.FC<QuestionProps> = ({
  question,
  onAnswer,
  selectedAnswer,
  isCorrect,
}) => {
  const wrongButtonRef = useRef<any>(null);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handleWrongAnswer = () => {
    if (wrongButtonRef.current) {
      wrongButtonRef.current.shake(400);
    }
  };

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <View style={styles.container}>
      <View style={styles.questionBox}>
        <Text style={styles.questionText}>{question.text}</Text>
      </View>
      {question.options.map((option, index) => {
        const isSelected = selectedAnswer === option;
        const isOptionCorrect = option === question.correctAnswer;
        return (
          <Animatable.View
            key={index}
            animation="bounceIn"
            delay={100 * index}
            ref={isSelected && isCorrect === false ? wrongButtonRef : null}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPressIn={handlePressIn}
              onPressOut={handlePressOut}
              onPress={() => {
                onAnswer(option);
                if (!isOptionCorrect && isSelected) handleWrongAnswer();
              }}
              disabled={!!selectedAnswer}>
              <Animated.View
                style={[
                  styles.optionButton,
                  {
                    transform: [{scale: scaleAnim}],
                    borderColor: isSelected
                      ? isCorrect
                        ? '#4CAF50' // Xanh lá nếu đúng
                        : '#F44336' // Đỏ nếu sai
                      : '#B0BEC5', // Xám nhạt nếu chưa chọn
                  },
                ]}>
                <View
                  style={[
                    styles.optionCircle,
                    {
                      backgroundColor: isSelected
                        ? isCorrect
                          ? '#4CAF50'
                          : '#F44336'
                        : 'transparent',
                    },
                  ]}
                />
                <Text style={styles.optionText}>
                  {index + 1}. {option}
                </Text>
              </Animated.View>
            </TouchableOpacity>
          </Animatable.View>
        );
      })}
      {selectedAnswer && (
        <Animatable.Text
          animation="fadeIn"
          duration={800}
          style={[
            styles.feedback,
            isCorrect ? styles.correctText : styles.wrongText,
          ]}>
          {isCorrect
            ? 'Correct!'
            : `Wrong! Correct answer: ${question.correctAnswer}`}
        </Animatable.Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  questionBox: {
    backgroundColor: '#FFF',
    borderWidth: 2,
    borderColor: '#FF9800', // Viền cam đậm hơn
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
    position: 'relative',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  questionText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    textAlign: 'center',
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    borderWidth: 2,
    borderColor: '#B0BEC5', // Viền xám nhạt
    borderRadius: 10,
    padding: 15,
    marginVertical: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 4,
  },
  optionCircle: {
    width: 16,
    height: 16,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#000',
    marginRight: 10,
    alignSelf: 'center',
  },
  optionText: {
    fontSize: 14,
    textAlignVertical: 'center',
    flex: 1,
  },
  feedback: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  correctText: {
    color: '#4CAF50', // Xanh lá
  },
  wrongText: {
    color: '#F44336', // Đỏ
  },
});

export default Question;

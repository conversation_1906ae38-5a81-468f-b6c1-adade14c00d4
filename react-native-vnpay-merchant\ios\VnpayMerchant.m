#import "VnpayMerchant.h"
#import <React/RCTLog.h>

@implementation VnpayMerchant

RCT_EXPORT_MODULE()

- (NSArray<NSString *> *)supportedEvents
{
  return @[@"PaymentBack"];
}

RCT_EXPORT_METHOD(show:(NSDictionary *)options
                 resolver:(RCTPromiseResolveBlock)resolve
                 rejecter:(RCTPromiseRejectBlock)reject)
{
  RCTLogInfo(@"VNPAY show called with options: %@", options);
  
  // For now, just log and resolve
  // In a real implementation, you would integrate with VNPAY iOS SDK here
  resolve(@{@"status": @"success", @"message": @"VNPAY not implemented yet"});
}

@end

import {useNavigation, DrawerActions} from '@react-navigation/native';
import {Image, TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ListTile, SkeletonImage, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {ScrollView} from 'react-native-gesture-handler';
import {navigate, RootScreen} from '../../../router/router';
import NotifCommunity from '../../notification/view/inCommunity';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {ProfileView} from './Chat';
import {useTranslation} from 'react-i18next';
import {LogoImg} from '../../../Screen/Page/Home';

export default function Notif() {
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      {/* header */}
      <ListTile
        style={{
          padding: 0,
          paddingBottom: 8,
          paddingHorizontal: 16,
        }}
        isClickLeading
        leading={
          <TouchableOpacity
            onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
            style={{padding: 4}}>
            <LogoImg />
          </TouchableOpacity>
        }
        title={t('notification.title')}
        trailing={<ProfileView />}
      />
      {/* content */}
      <NotifCommunity />
    </View>
  );
}

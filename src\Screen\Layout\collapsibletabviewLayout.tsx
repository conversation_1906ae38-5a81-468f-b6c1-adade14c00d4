import {Tab<PERSON>ar, TabView} from 'react-native-tab-view';
import {
  Animated,
  FlatList,
  Platform,
  Pressable,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import React, {useRef, useState} from 'react';
import {useRoute} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';

export default function CollapsibletabviewLayout() {
  const [activeTab, setActiveTab] = useState(0);
  const scrollY = useRef(new Animated.Value(0)).current;
  const headerHeight = 390; // Chiều cao phần header ban đầu
  const tabBarHeight = 50; // Chiều cao của tab bar
  const newHeaderHeight = 50; // Chiều cao của header mới

  // Animation cho header gốc
  const originalHeaderTranslate = scrollY.interpolate({
    inputRange: [0, headerHeight],
    outputRange: [0, -headerHeight],
    extrapolate: 'clamp',
  });

  const originalHeaderOpacity = scrollY.interpolate({
    inputRange: [0, headerHeight / 2, headerHeight],
    outputRange: [1, 0.5, 0],
    extrapolate: 'clamp',
  });

  // Animation cho header mới và tabbar
  const newHeaderOpacity = scrollY.interpolate({
    inputRange: [headerHeight - 20, headerHeight],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  // Shadow cho container cố định khi được ghim
  const fixedElementsShadow = scrollY.interpolate({
    inputRange: [headerHeight - 20, headerHeight],
    outputRange: [0, 0.3],
    extrapolate: 'clamp',
  });

  // Chuyển đổi giữa các tab
  const changeTab = (tab: any) => {
    setActiveTab(tab);
  };

  const tabs = [
    {Id: 0, Name: 'Discussion'},
    {Id: 1, Name: 'About'},
  ];

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      {/* Header mới - xuất hiện khi cuộn */}
      <Animated.View
        style={[
          styles.newHeader,
          {
            opacity: newHeaderOpacity,
            shadowOpacity: fixedElementsShadow,
          },
        ]}>
        <Text style={styles.newHeaderText}>New Compact Header</Text>
      </Animated.View>

      {/* Tabbar - di chuyển lên trên và được ghim */}
      <Animated.View
        style={[
          styles.tabBarContainer,
          {
            transform: [
              {
                translateY: scrollY.interpolate({
                  inputRange: [0, headerHeight],
                  outputRange: [0, -headerHeight + newHeaderHeight],
                  extrapolate: 'clamp',
                }),
              },
            ],
          },
        ]}>
        <View style={styles.tabBar}>
          {tabs.map(item => {
            return (
              <TouchableOpacity
                style={[
                  styles.tabItem,
                  activeTab === item.Id && styles.activeTabItem,
                ]}
                onPress={() => changeTab(item.Id)}>
                <Text
                  style={[
                    styles.tabText,
                    activeTab === item.Id && styles.activeTabText,
                  ]}>
                  {item.Name}
                </Text>
                {activeTab === item.Id && <View style={styles.indicator} />}
              </TouchableOpacity>
            );
          })}
        </View>
      </Animated.View>

      {/* Content  */}
      <Animated.ScrollView
        bounces={false}
        contentContainerStyle={{paddingTop: headerHeight + tabBarHeight}}
        scrollEventThrottle={16}
        onScroll={Animated.event(
          [{nativeEvent: {contentOffset: {y: scrollY}}}],
          {useNativeDriver: true},
        )}>
        {/* Header gốc - biến mất khi cuộn */}
        <Animated.View style={[styles.originalHeader]}>
          <Pressable>
            <Text style={styles.originalHeaderText}>Original Header</Text>
          </Pressable>
        </Animated.View>
        {activeTab === 0 ? <View /> : <View />}
      </Animated.ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  originalHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 390,
    backgroundColor: '#f8f8f8',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  originalHeaderText: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  newHeader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 50,
    backgroundColor: '#e8e8e8',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 3,
  },
  newHeaderText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tabBarContainer: {
    position: 'absolute',
    top: 390, // Bắt đầu dưới header gốc
    left: 0,
    right: 0,
    height: 50,
    backgroundColor: '#fff',
    zIndex: 2,
    borderBottomWidth: 0.5,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    height: '100%',
    alignItems: 'center',
  },
  tabItem: {
    paddingVertical: 15,
    marginRight: 24,
    position: 'relative',
  },
  activeTabItem: {
    // Style cho tab active
  },
  tabText: {
    ...TypoSkin.label4,
  },
  activeTabText: {
    color: ColorThemes.light.Primary_Color_Main,
  },
  indicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 1.5,
    backgroundColor: ColorThemes.light.Primary_Color_Main,
  },
});

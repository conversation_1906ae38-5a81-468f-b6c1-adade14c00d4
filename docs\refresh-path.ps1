# Script để refresh PATH trong PowerShell session hiện tại

Write-Host "=== Refreshing PATH in current session ===" -ForegroundColor Green

# Refresh PATH
$Machine = [System.Environment]::GetEnvironmentVariable("Path", "Machine")
$User = [System.Environment]::GetEnvironmentVariable("Path", "User")
$env:Path = $Machine + ";" + $User

Write-Host "PATH refreshed!" -ForegroundColor Green

# Test Git
Write-Host "`nTesting Git..." -ForegroundColor Yellow
try {
    $gitVersion = git --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Git is working: $gitVersion" -ForegroundColor Green
    } else {
        Write-Host "Git command failed" -ForegroundColor Red
    }
} catch {
    Write-Host "Git not found in PATH" -ForegroundColor Red
}

# Show current PATH entries
Write-Host "`nCurrent PATH contains Git entries:" -ForegroundColor Cyan
$env:Path -split ';' | Where-Object { $_ -like "*git*" } | ForEach-Object {
    Write-Host "  $_"
} 
# 🚀 Generic Game System Migration Guide

## Overview

This guide helps you migrate existing games to the new Generic Game System and create new games efficiently.

## 📋 Table of Contents

1. [System Architecture](#system-architecture)
2. [Migration Steps](#migration-steps)
3. [Creating New Games](#creating-new-games)
4. [Customization Options](#customization-options)
5. [Testing & Validation](#testing--validation)
6. [Troubleshooting](#troubleshooting)

## 🏗️ System Architecture

### Core Components

```
src/modules/game/
├── components/
│   ├── GenericGameHomeScreen.tsx     # Main game configurations
│   ├── GamePathConfig.ts             # Path configurations
│   ├── BackgroundPathMapping.ts      # Background-path mappings
│   └── ChooseLevelModal.tsx          # Reusable level selector
├── hooks/
│   └── useGenericSVGPath.ts          # Generic path hooks
├── utils/
│   └── GameTestUtils.ts              # Testing utilities
└── scripts/
    ├── testGenericSystem.ts          # Comprehensive tests
    └── demoGenericSystem.ts          # Demo & examples
```

### Key Benefits

- ✅ **80% less code** for new games
- ✅ **Consistent behavior** across all games
- ✅ **Responsive design** automatic
- ✅ **Easy customization** through configs
- ✅ **Type-safe** configurations
- ✅ **Comprehensive testing** built-in

## 🔄 Migration Steps

### Step 1: Backup Existing Code

```bash
# Create backup of existing game
cp -r src/modules/game/yourgame src/modules/game/yourgame_backup
```

### Step 2: Add Game Configuration

Add your game to `src/modules/game/components/GenericGameHomeScreen.tsx`:

```typescript
'YOUR_GAME': {
  gameId: 'YOUR_GAME',
  gameName: 'Your Game Name',
  backgroundImage: require('../yourgame/assets/background.png'),
  milestoneImages: {
    completed: require('../yourgame/assets/completed.png'),
    inProgress: require('../yourgame/assets/inprogress.png'),
    locked: require('../yourgame/assets/locked.png'),
  },
  birdImage: require('../yourgame/assets/character.png'),
  footerIcons: {
    coin: require('../yourgame/assets/coin.png'),
    rank: require('../yourgame/assets/rank.png'),
    level: require('../yourgame/assets/level.png'),
    help: require('../yourgame/assets/help.png'),
  },
  modalContent: 'Your game rules\nand instructions',
  helpText: 'Help text for your game',
  startGameScreen: 'StartYourGame',
  colors: {
    primary: '#YOUR_PRIMARY_COLOR',
    footer: '#YOUR_FOOTER_COLOR',
    text: '#YOUR_TEXT_COLOR',
  },
},
```

### Step 3: Add Path Configuration

Add your game to `src/modules/game/components/GamePathConfig.ts`:

```typescript
'YOUR_GAME': {
  gameId: 'YOUR_GAME',
  pathStyle: 'curved', // or 'zigzag', 'spiral', 'wave', 'linear'
  milestonePositions: [
    {id: 1, top: 0.8, left: 0.3, levelName: 'Beginner'},
    {id: 2, top: 0.6, left: 0.7, levelName: 'Intermediate'},
    // ... more positions
  ],
  pathConfig: {
    curvature: 0.6,
    smoothness: 0.8,
    direction: 'clockwise',
  },
},
```

### Step 4: Update Home Screen

Replace your existing homeScreen.tsx:

```typescript
import React from 'react';
import {useRoute} from '@react-navigation/native';
import GenericGameHomeScreen from '../components/GenericGameHomeScreen';

const YourGameHomeScreen: React.FC = () => {
  const route = useRoute<any>();
  const {gameId} = route.params || {gameId: 'YOUR_GAME'};

  return (
    <GenericGameHomeScreen
      gameId={gameId}
      // Optional: Custom components
      // HeaderComponent={CustomHeader}
      // FooterComponent={CustomFooter}
    />
  );
};

export default YourGameHomeScreen;
```

### Step 5: Test Migration

```typescript
import {testGame} from '../utils/GameTestUtils';

// Test your migrated game
testGame('YOUR_GAME');
```

## 🆕 Creating New Games

### Quick Start (5 minutes)

1. **Add Game Config** (2 min):
   ```typescript
   // In GenericGameHomeScreen.tsx
   'NEW_GAME': { /* config */ }
   ```

2. **Add Path Config** (2 min):
   ```typescript
   // In GamePathConfig.ts
   'NEW_GAME': { /* path config */ }
   ```

3. **Create Screen** (1 min):
   ```typescript
   const NewGameScreen = () => (
     <GenericGameHomeScreen gameId="NEW_GAME" />
   );
   ```

### Path Style Options

#### Curved Path (Recommended for Quiz Games)
```typescript
pathStyle: 'curved',
pathConfig: {
  curvature: 0.6,    // 0-1, higher = more curved
  smoothness: 0.8,   // 0-1, higher = smoother
  direction: 'clockwise',
}
```

#### Zigzag Path (Good for Action Games)
```typescript
pathStyle: 'zigzag',
// No additional config needed
```

#### Spiral Path (Great for Adventure Games)
```typescript
pathStyle: 'spiral',
pathConfig: {
  curvature: 0.8,
  direction: 'counterclockwise',
}
```

#### Wave Path (Perfect for Puzzle Games)
```typescript
pathStyle: 'wave',
pathConfig: {
  amplitude: 0.3,    // Wave height
  curvature: 0.5,    // Wave smoothness
}
```

## 🎨 Customization Options

### Custom Components

```typescript
<GenericGameHomeScreen
  gameId="YOUR_GAME"
  HeaderComponent={CustomHeader}
  FooterComponent={CustomFooter}
  MilestoneComponent={CustomMilestone}
  StartGameModalComponent={CustomModal}
/>
```

### Custom Header Example

```typescript
const CustomHeader = ({selectedLevel, gameConfig}) => (
  <View style={styles.customHeader}>
    <Text style={{color: gameConfig.colors.text}}>
      {gameConfig.gameName} - {selectedLevel}
    </Text>
  </View>
);
```

### Custom Milestone Example

```typescript
const CustomMilestone = ({status, number, onPress, gameConfig}) => (
  <TouchableOpacity onPress={onPress}>
    <View style={[styles.milestone, {borderColor: gameConfig.colors.primary}]}>
      <Text>{number}</Text>
    </View>
  </TouchableOpacity>
);
```

### Color Customization

```typescript
colors: {
  primary: '#FF6B35',      // Milestone numbers, borders
  footer: '#FF8E53',       // Footer background
  text: '#FFFFFF',         // Header text
  // Add custom colors
  accent: '#FFB84D',
  background: '#F5F5F5',
}
```

## 🧪 Testing & Validation

### Comprehensive Testing

```typescript
import {testGame, testAllGames} from '../utils/GameTestUtils';

// Test specific game
const result = testGame('YOUR_GAME');
console.log(result.overallSuccess ? 'PASS' : 'FAIL');

// Test all games
testAllGames();
```

### Device Testing

```typescript
import {getCurrentDevice} from '../utils/GameTestUtils';

// Check current device compatibility
const deviceInfo = getCurrentDevice();
console.log(`Testing on: ${deviceInfo.closestDevice}`);
```

### Performance Testing

```typescript
import {performanceDemo} from '../scripts/demoGenericSystem';

// Test performance metrics
performanceDemo();
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Assets Not Found
```
Error: Cannot resolve module '../yourgame/assets/background.png'
```
**Solution**: Ensure all asset paths are correct and files exist.

#### 2. Path Generation Fails
```
Error: Failed to generate path data
```
**Solution**: Check milestone positions are within 0-1 range.

#### 3. Positions Out of Bounds
```
Error: Positions out of expected range
```
**Solution**: Validate milestone positions using test utilities.

### Debug Mode

Enable debug logging:

```typescript
// In your game screen
const DEBUG = __DEV__;

if (DEBUG) {
  console.log('Game Config:', getGameConfig(gameId));
  console.log('Path Config:', getGamePathConfig(gameId));
}
```

### Validation Checklist

- [ ] Game config has all required fields
- [ ] All asset paths are valid
- [ ] Milestone positions are 0-1 range
- [ ] Path generation works
- [ ] Colors are valid hex codes
- [ ] Game ID is unique
- [ ] Tests pass on multiple devices

## 📊 Performance Guidelines

### Optimization Tips

1. **Asset Optimization**:
   - Use optimized images (WebP when possible)
   - Keep background images under 500KB
   - Use vector icons for milestones

2. **Path Complexity**:
   - Limit milestones to 10-15 per game
   - Use simpler paths for low-end devices
   - Cache generated paths

3. **Memory Management**:
   - Preload only essential assets
   - Lazy load game-specific resources
   - Clean up unused components

### Performance Targets

- **Path Generation**: < 5ms
- **Position Calculation**: < 10ms
- **Screen Render**: < 100ms
- **Memory Usage**: < 50MB additional

## 🚀 Advanced Features

### Dynamic Paths

```typescript
// Path that changes based on player progress
const dynamicPathConfig = {
  pathStyle: 'adaptive',
  adaptationRules: {
    beginner: 'linear',
    intermediate: 'curved',
    advanced: 'spiral',
  },
};
```

### Multi-Language Support

```typescript
// Add to game config
i18n: {
  en: {
    gameName: 'Your Game',
    modalContent: 'Game rules in English',
  },
  vi: {
    gameName: 'Game Của Bạn',
    modalContent: 'Luật chơi bằng tiếng Việt',
  },
},
```

### Analytics Integration

```typescript
// Track game events
onMilestonePress: (status, number) => {
  Analytics.track('milestone_pressed', {
    gameId: 'YOUR_GAME',
    milestoneNumber: number,
    status: status,
  });
},
```

## 📞 Support

For additional help:

1. Check the test utilities: `GameTestUtils.ts`
2. Run the demo: `demoGenericSystem.ts`
3. Review existing game implementations
4. Create an issue with detailed error logs

---

**Happy Gaming! 🎮**

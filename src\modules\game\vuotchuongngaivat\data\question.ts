import {MiniQuestion, Question} from '../models/models';

export const miniQuestions: MiniQuestion[] = [
  {
    id: '1',
    text: 'Là một trò chơi dân gian nổi tiếng.',
    length: 5,
    keyIndex: 2,
    indexStart: 4,
    listWord: [
      {
        id: '1',
        text: 'す',
        isKey: false,
        position: 1,
      },
      {
        id: '2',
        text: 'ぜ',
        isKey: true,
        position: 2,
      },
      {
        id: '3',
        text: 'な',
        isKey: false,
        position: 3,
      },
      {
        id: '4',
        text: 'り',
        isKey: false,
        position: 4,
      },
      {
        id: '5',
        text: 'れ',
        isKey: false,
        position: 5,
      },
    ],
  },
  {
    id: '2',
    text: 'Là một trò chơi dân gian nổi tiếng.',
    length: 5,
    keyIndex: 4,
    indexStart: 1,
    listWord: [
      {
        id: '1',
        text: 'ナ',
        isKey: false,
        position: 1,
      },
      {
        id: '2',
        text: 'ナ',
        isKey: false,
        position: 2,
      },
      {
        id: '3',
        text: 'ナ',
        isKey: false,
        position: 3,
      },
      {
        id: '4',
        text: 'ナ',
        isKey: true,
        position: 4,
      },
      {
        id: '5',
        text: 'ナ',
        isKey: false,
        position: 5,
      },
    ],
  },
];

export const question: Question = {
  id: '1',
  text: 'Là một trò chơi dân gian nổi tiếng thường được trẻ em chơi ở các vùng quê.',
  hint: 'Trò chơi dân gian',
  answer: '121232',
  miniQuestions,
};

import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {AppButton, ListTile, SkeletonImage} from 'wini-mobile-components';
import EmptyPage from '../../../../Screen/emptyPage';
import {navigate, RootScreen} from '../../../../router/router';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {useDispatch, useSelector} from 'react-redux';
import {GroupActions} from '../../reducers/groupReducer';
import {followingGroupsActions} from '../../reducers/followingGroupsReducer';
import {AppDispatch, RootState} from '../../../../redux/store/store';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {useSelectorCustomerState} from '../../../../redux/hook/customerHook';

interface Props {
  titleList?: string;
  isSeeMore?: boolean;
  onPressSeeMore?: () => void;
}

export default function SocialGroups(props: Props) {
  const dispatch: AppDispatch = useDispatch();
  const {groups, isLoading, error, hasMore, page} = useSelector(
    (state: RootState) => state.group,
  );
  const followingGroups = useSelector(
    (state: RootState) => state.followingGroups.groups,
  );
  const creatorGroups = useSelector(
    (state: RootState) => state.myGroups.groups,
  );

  const [activeTab, setActiveTab] = useState(0);

  const customer = useSelectorCustomerState().data;

  useEffect(() => {
    loadGroups();
  }, [activeTab]);

  const loadGroups = (refresh = true) => {
    if (refresh) {
      dispatch(GroupActions.getAllGroups(1, 10));
    } else if (hasMore) {
      dispatch(GroupActions.getAllGroups(page + 1, 10));
    }
  };

  const handleJoinGroup = async (group: any) => {
    dispatch(followingGroupsActions.followGroup(group));
    dispatch(GroupActions.updateMemberCount(group.Id, 1));
  };

  const isFollowing = (groupId: string) => {
    return (
      followingGroups.some(group => group.Id === groupId) ||
      creatorGroups.some(group => group.Id === groupId)
    );
  };

  const tabs = [
    {Id: 0, Name: 'For you'},
    {Id: 1, Name: 'Popular'},
    {Id: 2, Name: 'Following'},
    {Id: 3, Name: 'Watch'},
  ];

  const TabBar = () => {
    return (
      <ScrollView
        showsHorizontalScrollIndicator={false}
        horizontal={true}
        contentContainerStyle={{
          gap: 8,
        }}
        style={styles.tabBar}>
        {/* {tabs.map((tab, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => setActiveTab(index)}
            style={[styles.tab, activeTab === index && styles.activeTab]}>
            <Text style={[styles.tabText]}>{tab.Name}</Text>
          </TouchableOpacity>
        ))} */}
      </ScrollView>
    );
  };

  const renderItem = ({item}: any) => {
    const following = isFollowing(item.Id);

    return (
      <ListTile
        key={item.Id}
        onPress={() => {
          navigate(RootScreen.GroupIndex, {Id: item.Id});
        }}
        style={{
          borderColor: ColorThemes.light.Neutral_Border_Color_Main,
          borderWidth: 1,
          marginBottom: 16,
        }}
        title={item.Name}
        titleStyle={{
          ...TypoSkin.heading7,
          color: ColorThemes.light.Neutral_Text_Color_Title,
        }}
        subtitle={
          <Text
            style={{
              ...TypoSkin.subtitle3,
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
            }}>
            {item.MemberCount} thành viên
          </Text>
        }
        leading={
          <SkeletonImage
            key={item.Thumb}
            source={
              item.Thumb
                ? {
                    uri: `${
                      item.Thumb
                        ? ConfigAPI.urlImg + item.Thumb
                        : 'https://placehold.co/56/FFFFFF/000000/png'
                    }`,
                  }
                : require('../../../../assets/appstore.png')
            }
            height={56}
            width={56}
            style={{
              height: 56,
              width: 56,
              borderRadius: 100,
              backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
            }}
          />
        }
        bottom={
          <View style={{width: '100%', flex: 1, paddingTop: 8, gap: 8}}>
            {item.Description && (
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.Neutral_Text_Color_Body,
                }}>
                {item.Description}
              </Text>
            )}
            {following ||
            item.CustomerId === customer?.Id ||
            !customer?.Id ? null : (
              <AppButton
                title={following ? 'Đã tham gia' : 'Tham gia'}
                onPress={() => (following ? null : handleJoinGroup(item))}
                textStyle={{
                  ...TypoSkin.buttonText5,
                  color: following
                    ? ColorThemes.light.Neutral_Text_Color_Subtitle
                    : ColorThemes.light.Info_Color_Main,
                }}
                backgroundColor={ColorThemes.light.transparent}
                borderColor={
                  following
                    ? ColorThemes.light.Neutral_Text_Color_Subtitle
                    : ColorThemes.light.Info_Color_Main
                }
                containerStyle={{
                  borderRadius: 8,
                  paddingHorizontal: 8,
                  alignItems: 'center',
                  alignSelf: 'baseline',
                  height: 24,
                }}
              />
            )}
          </View>
        }
      />
    );
  };

  const renderEmptyComponent = () => {
    if (isLoading) {
      return (
        <View style={{gap: 16}}>
          <GroupCardShimmer />
          <GroupCardShimmer />
          <GroupCardShimmer />
        </View>
      );
    }
  };

  return (
    <View style={{}}>
      {props.titleList ? (
        <View
          style={{
            paddingHorizontal: 16,
            paddingBottom: 8,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}>
          <Text
            style={{
              ...TypoSkin.heading7,
              color: ColorThemes.light.Neutral_Text_Color_Title,
            }}>
            Khám phá các nhóm khác
          </Text>
          <TabBar />
        </View>
      ) : null}
      <FlatList
        data={groups}
        nestedScrollEnabled={true}
        renderItem={renderItem}
        keyExtractor={item => item.Id}
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        style={{width: '100%', height: '100%', paddingHorizontal: 16}}
        onRefresh={loadGroups}
        refreshing={isLoading}
        onEndReached={() => loadGroups(false)}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={renderEmptyComponent}
        ListFooterComponent={() => {
          return <View style={{height: 24}} />;
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    paddingVertical: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 17,
    borderRadius: 20,
    borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
    borderWidth: 1,
    height: 24,
  },
  activeTab: {
    backgroundColor: ColorThemes.light.primary_light_color,
    borderColor: ColorThemes.light.Primary_Color_Main,
  },
  tabText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Neutral_Text_Color_Body,
  },
});

export const GroupCardShimmer = () => {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View
        style={{
          borderColor: ColorThemes.light.Neutral_Border_Color_Main,
          borderWidth: 1,
          padding: 16,
          borderRadius: 8,
          gap: 12,
        }}>
        {/* Header row with avatar and title */}
        <View style={{flexDirection: 'row', gap: 12}}>
          {/* Avatar placeholder */}
          <View
            style={{
              width: 56,
              height: 56,
              borderRadius: 28,
            }}
          />

          {/* Title and member count */}
          <View style={{gap: 4, flex: 1}}>
            <View
              style={{
                width: '60%',
                height: 20,
                borderRadius: 4,
              }}
            />
            <View
              style={{
                width: '40%',
                height: 16,
                borderRadius: 4,
              }}
            />
          </View>
        </View>

        {/* Description placeholder */}
        <View style={{gap: 4}}>
          <View
            style={{
              width: '100%',
              height: 16,
              borderRadius: 4,
            }}
          />
          <View
            style={{
              width: '80%',
              height: 16,
              borderRadius: 4,
            }}
          />
        </View>

        {/* Join button placeholder */}
        <View
          style={{
            width: 60,
            height: 24,
            borderRadius: 8,
          }}
        />
      </View>
    </SkeletonPlaceholder>
  );
};

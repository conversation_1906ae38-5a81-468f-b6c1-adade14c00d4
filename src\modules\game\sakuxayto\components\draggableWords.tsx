import React from 'react';
import {useRef} from 'react';
import {
  Animated,
  Easing,
  PanResponder,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';

// Draggable Word Component in sentence - can be dragged to reorder
interface DraggableWordInSentenceProps {
  word: string;
  index: number;
  isSubmitted: boolean;
  isError: boolean;
  isInserting?: boolean;
  insertIndex: number;
  onDragStart: (word: string, index: number) => void;
  onDragMove: (x: number, y: number) => void;
  onDragEnd: (word: string, fromIndex: number, x: number, y: number) => void;
  isDragging: boolean;
  dragId?: string; // Unique identifier for this word instance
}

const DraggableWordInSentence = React.memo(
  ({
    word,
    index,
    isSubmitted,
    isInserting,
    insertIndex,
    onDragStart,
    onDragMove,
    onDragEnd,
    isDragging,
    isError,
  }: DraggableWordInSentenceProps) => {
    const translateX = useRef(new Animated.Value(0)).current;
    const scale = useRef(new Animated.Value(1)).current;
    const pan = useRef(new Animated.ValueXY()).current;
    const dragScale = useRef(new Animated.Value(1)).current;

    // Smooth animation for sentence reordering
    React.useEffect(() => {
      if (isDragging) {
        // Don't animate position when this word is being dragged
        return;
      }

      if (isInserting) {
        // Smooth animation to make space for new word
        const shouldMoveRight = index >= insertIndex;
        const moveDistance = shouldMoveRight ? 70 : 0; // Slightly larger space for better visibility

        Animated.parallel([
          Animated.timing(translateX, {
            toValue: moveDistance,
            duration: 400, // Slower, smoother animation
            easing: Easing.out(Easing.cubic), // Smoother easing
            useNativeDriver: true,
          }),
          Animated.sequence([
            Animated.timing(scale, {
              toValue: 0.92, // Less dramatic scale change
              duration: 200,
              easing: Easing.out(Easing.quad),
              useNativeDriver: true,
            }),
            Animated.timing(scale, {
              toValue: 1,
              duration: 200,
              easing: Easing.out(Easing.cubic),
              useNativeDriver: true,
            }),
          ]),
        ]).start();
      } else {
        // Smooth reset animation
        Animated.parallel([
          Animated.timing(translateX, {
            toValue: 0,
            duration: 350, // Smoother reset
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1,
            duration: 300,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
        ]).start();
      }
    }, [isInserting, insertIndex, index, translateX, scale, isDragging]);

    // Pan responder for dragging words in sentence - recreate on each render to ensure fresh state
    const panResponder = React.useMemo(
      () =>
        PanResponder.create({
          onStartShouldSetPanResponder: () => {
            console.log(
              `🔥 onStartShouldSetPanResponder for word "${word}" at index ${index}, isSubmitted=${isSubmitted}`,
            );
            return !isSubmitted;
          },
          onMoveShouldSetPanResponder: () => {
            console.log(
              `🔥 onMoveShouldSetPanResponder for word "${word}" at index ${index}, isSubmitted=${isSubmitted}`,
            );
            return !isSubmitted;
          },
          onPanResponderGrant: () => {
            console.log(
              `🔥 onPanResponderGrant for word "${word}" at index ${index}`,
            );
            onDragStart(word, index);
            Animated.spring(dragScale, {
              toValue: 1.1,
              useNativeDriver: true,
            }).start();
          },
          onPanResponderMove: (_, gestureState) => {
            pan.setValue({x: gestureState.dx, y: gestureState.dy});
            onDragMove(gestureState.moveX, gestureState.moveY);
          },
          onPanResponderRelease: (_, gestureState) => {
            Animated.spring(dragScale, {
              toValue: 1,
              useNativeDriver: true,
            }).start();
            onDragEnd(word, index, gestureState.moveX, gestureState.moveY);
            pan.setValue({x: 0, y: 0});
          },
          onPanResponderTerminate: () => {
            Animated.spring(dragScale, {
              toValue: 1,
              useNativeDriver: true,
            }).start();
            pan.setValue({x: 0, y: 0});
          },
        }),
      [
        word,
        index,
        isSubmitted,
        onDragStart,
        onDragMove,
        onDragEnd,
        dragScale,
        pan,
      ],
    );

    return (
      <>
        {/* Main draggable word */}
        <Animated.View
          {...panResponder.panHandlers}
          style={[
            styles.word,
            isSubmitted && styles.wordSubmitted,
            isError && styles.wordError,
            isDragging && styles.wordDragging,
            {
              transform: [
                {translateX: isDragging ? pan.x : translateX},
                {translateY: isDragging ? pan.y : 0},
                {scale: Animated.multiply(scale, dragScale)},
              ],
              zIndex: isDragging ? 1000 : 1,
              opacity: isDragging ? 0.9 : 1, // Slightly transparent when dragging
            },
          ]}>
          <Text
            style={[
              styles.wordText,
              isSubmitted && styles.wordTextSubmitted,
              isError && styles.wordTextError,
            ]}>
            {word}
          </Text>
        </Animated.View>
      </>
    );
  },
);

interface DraggableWordProps {
  word: string;
  used?: boolean;
  onDragEnd: (word: string, x: number, y: number) => void;
  onDragStart: () => void;
  onDragMove: (x: number, y: number) => void;
}

const DraggableWord = React.memo(
  ({word, used, onDragEnd, onDragStart, onDragMove}: DraggableWordProps) => {
    const pan = useRef(new Animated.ValueXY()).current;
    const scale = useRef(new Animated.Value(1)).current;

    const panResponder = useRef(
      PanResponder.create({
        onStartShouldSetPanResponder: () => true,
        onMoveShouldSetPanResponder: () => true,
        onPanResponderGrant: () => {
          //console.log('Drag started');
          onDragStart();
          Animated.spring(scale, {
            toValue: 1.1,
            useNativeDriver: true,
          }).start();
        },
        onPanResponderMove: (_, gestureState) => {
          //console.log('Dragging:', gestureState.moveX, gestureState.moveY);
          pan.setValue({x: gestureState.dx, y: gestureState.dy});
          onDragMove(gestureState.moveX, gestureState.moveY);
        },
        onPanResponderRelease: (_, gestureState) => {
          //console.log('Drag released at:', gestureState.moveX, gestureState.moveY);
          Animated.spring(scale, {
            toValue: 1,
            useNativeDriver: true,
          }).start();
          onDragEnd(word, gestureState.moveX, gestureState.moveY);
          pan.setValue({x: 0, y: 0});
        },
        onPanResponderTerminate: () => {
          console.log('Drag terminated');
          Animated.spring(scale, {
            toValue: 1,
            useNativeDriver: true,
          }).start();
          pan.setValue({x: 0, y: 0});
        },
      }),
    ).current;

    // if (used) {
    //   return (
    //     <View style={[styles.draggable, styles.usedDraggable]}>
    //       <Text style={styles.dragText}>{word}</Text>
    //     </View>
    //   );
    // }

    return (
      <Animated.View
        {...panResponder.panHandlers}
        style={[
          styles.draggable,
          {
            transform: [{translateX: pan.x}, {translateY: pan.y}, {scale}],
          },
        ]}>
        <Text style={styles.dragText}>{word}</Text>
      </Animated.View>
    );
  },
);

export {DraggableWord, DraggableWordInSentence};

const styles = StyleSheet.create({
  word: {
    padding: 8,
    backgroundColor: '#F1D1A6',
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#FFFFFFFF',
    minWidth: 40,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 9,
  },
  wordText: {
    fontSize: 16,
    color: '#112164',
    fontWeight: '500',
    textAlign: 'center',
  },
  wordError: {
    backgroundColor: ColorThemes.light.Error_Color_Background,
    borderColor: ColorThemes.light.Error_Color_Border,
  },
  wordTextError: {
    color: ColorThemes.light.Error_Color_Main,
  },
  wordSubmitted: {
    backgroundColor: '#c8e6c9',
    borderColor: '#4caf50',
  },
  wordTextSubmitted: {
    color: '#2e7d32',
  },
  draggable: {
    padding: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
    backgroundColor: '#F1D1A6',
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#FFFFFFFF',
    minWidth: 40,
  },
  dragText: {
    fontSize: 16,
    color: '#112164',
    fontWeight: '500',
  },
  usedDraggable: {
    backgroundColor: '#bdbdbd',
    opacity: 0.5,
  },
  wordDragging: {
    backgroundColor: '#e8f5e8',
    borderColor: '#4caf50',
    shadowColor: '#4caf50',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 10,
  },
});

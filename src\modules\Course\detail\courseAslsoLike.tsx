/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {View, Text, FlatList} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {AppButton} from 'wini-mobile-components';
import {CourseDA} from '../da';
import {SkeletonPlaceCard} from '../../Default/card/defaultImage';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../router/router';
import {useTranslation} from 'react-i18next';
import DefaultBanner from '../../community/news/card/infor';
import {DefaultProduct} from '../../Default/card/defaultProduct';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
  id: string;
  cateId: string;
}

export default function CourseAslsoLike(props: Props) {
  const [data, setData] = useState<Array<any>>([]);
  const navigation = useNavigation<any>();

  const [isLoading, setLoading] = useState(true);
  const {t} = useTranslation();
  const [listItems, setlistItems] = useState<Array<any>>([]);
  const courseDA = new CourseDA();
  useEffect(() => {
    ongetData();
  }, []);

  const ongetData = async () => {
    setLoading(true);
    const result = await courseDA.getAllListbyCategory(
      null,
      null,
      props.cateId,
    );
    if (result) {
      const tmp = result.data.filter((item: any) => item.Id !== props.id);
      var lst = await Promise.all(
        result.data.map(async (item: {Id: string}) => ({
          id: item.Id,
          icon: 'outline/education/hat-3',
          title: `${await courseDA.countStudentCourse(item.Id)} ${t(
            'student',
          )}`,
        })),
      );
      setlistItems(lst);
      setData(tmp ?? []);
    }
    setLoading(false);
  };

  return (
    <View
      style={{
        height: props.horizontal ? 555 : undefined,
      }}>
      {props.titleList ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            paddingBottom: 16,
            flexDirection: 'row',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}>
          <Text
            style={{
              ...TypoSkin.heading5,
              color: ColorThemes.light.Neutral_Text_Color_Title,
            }}>
            {props.titleList}
          </Text>
          {props.isSeeMore ? (
            <AppButton
              title={'See more'}
              containerStyle={{
                justifyContent: 'flex-start',
                alignSelf: 'baseline',
              }}
              backgroundColor={'transparent'}
              textStyle={TypoSkin.buttonText3}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={() => {
                navigation.push(RootScreen.listCoursebyCate, {
                  id: props.cateId,
                  title: props.titleList,
                });
              }}
              textColor={ColorThemes.light.Info_Color_Main}
            />
          ) : null}
        </View>
      ) : null}
      <FlatList
        data={data}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        renderItem={({item, index}) => {
          return (
            <DefaultProduct
              key={index}
              listItems={listItems?.filter(t => t.id === item.Id)}
              onPressDetail={() => {
                navigation.push(RootScreen.CourseDetail, {id: item.Id});
              }}
              containerStyle={{paddingHorizontal: 16}}
              onPressLikeAction={async () => {
                if (item.IsLike === true) {
                  const result = await courseDA.deleteWishlistCourse(item.Id);
                  if (result.code === 200) {
                    setData(prevData =>
                      prevData.map(a =>
                        a.Id === item.Id ? {...item, IsLike: false} : item,
                      ),
                    );
                  }
                } else {
                  const result = await courseDA.addWishlistCourse(item.Id);
                  if (result) {
                    setData(prevData =>
                      prevData.map(a =>
                        a.Id === item.Id ? {...item, IsLike: true} : item,
                      ),
                    );
                  }
                }
              }}
              data={item}
              titleStyle={{
                ...TypoSkin.heading7,
                color: ColorThemes.light.Neutral_Text_Color_Body,
              }}
            />
          );
        }}
        style={{width: '100%', height: '100%'}}
        keyExtractor={item => item.Id}
        horizontal={props.horizontal}
        ListEmptyComponent={() => {
          if (isLoading) {
            return <SkeletonPlaceCard />;
          }
          return <Text style={{color: '#000000'}}>{t('nodata')}</Text>;
        }}
      />
    </View>
  );
}

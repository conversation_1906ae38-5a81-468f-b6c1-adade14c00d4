import { NativeModules } from 'react-native';

const { VnpayMerchant } = NativeModules;

// Export the native module
export const VnpayMerchantModule = VnpayMerchant;

// Export default methods
export default {
  show: (options) => {
    if (VnpayMerchant && VnpayMerchant.show) {
      return VnpayMerchant.show(options);
    } else {
      console.warn('VnpayMerchant native module not found');
      return Promise.reject(new Error('VnpayMerchant native module not found'));
    }
  }
};

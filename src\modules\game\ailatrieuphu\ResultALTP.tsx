import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Image,
  Dimensions,
  ActivityIndicator
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import * as Animatable from 'react-native-animatable';
// import Sound from 'react-native-sound'; // Sẽ import bằng tay

import { AppDispatch, RootState } from '../../../redux/store/store';
import { resetGame, gameAction } from './redux/gameSlice';
import { formatMoney } from './data/questionSets';
import { GameStatus } from '../../../Config/Contanst';
import ConfigAPI from '../../../Config/ConfigAPI';

const { width } = Dimensions.get('window');

const ResultALTP = () => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<AppDispatch>();
  const gameState = useSelector((state: RootState) => state.game);

  // State để theo dõi trạng thái cập nhật milestone
  const [isUpdating, setIsUpdating] = useState(true);
  const [updateSuccess, setUpdateSuccess] = useState(false);

  // Xác định xem người chơi đã hoàn thành milestone chưa
  const isCompleted = gameState.isGameWon ||
    (gameState.currentQuestionIndex >= 14 && !gameState.isGameOver);

  // Ref cho âm thanh
  // const resultSoundRef = useRef<Sound | null>(null);

  useEffect(() => {
    // Khởi tạo âm thanh
    // Sound.setCategory('Playback');

    // resultSoundRef.current = new Sound(
    //   gameState.isGameWon ? 'win.mp3' : 'game_over.mp3',
    //   Sound.MAIN_BUNDLE,
    //   error => {
    //     if (error) console.error('Error loading result sound:', error);
    //     else resultSoundRef.current?.play();
    //   }
    // );

    // Cập nhật trạng thái milestone
    const updateMilestoneStatus = async () => {
      try {
        setIsUpdating(true);

        // Lấy thông tin milestone hiện tại
        const currentMilestoneId = gameState.currentMilestoneId;
        if (!currentMilestoneId) {
          setIsUpdating(false);
          return;
        }

        // Xác định trạng thái mới
        const newStatus = isCompleted ? GameStatus.Completed : GameStatus.Pending;

        // Cập nhật trạng thái milestone
        const success = await dispatch(
          gameAction.updateMilestoneStatus(
            currentMilestoneId,
            newStatus,
            gameState.currentMoney
          )
        );

        setUpdateSuccess(!!success);
      } catch (error) {
        console.error('Error updating milestone status:', error);
        setUpdateSuccess(false);
      } finally {
        setIsUpdating(false);
      }
    };

    updateMilestoneStatus();

    return () => {
      // resultSoundRef.current?.release();
    };
  }, [dispatch, gameState.currentMilestoneId, gameState.currentMoney, isCompleted]);

  const handlePlayAgain = async () => {
    try {
      // Reset game state
      dispatch(resetGame());

      // Đảm bảo dữ liệu milestone được cập nhật khi quay lại màn hình Home
      await dispatch(gameAction.getMilestones(ConfigAPI.gameALTP));

      // Điều hướng về màn hình Home
      navigation.replace('HomeALTP');
    } catch (error) {
      console.error('Error updating milestones:', error);
      // Vẫn điều hướng về màn hình Home ngay cả khi có lỗi
      navigation.replace('HomeALTP');
    }
  };

  const getResultMessage = () => {
    if (gameState.isGameWon) {
      return 'Chúc mừng! Bạn đã trở thành triệu phú!';
    } else if (gameState.currentMoney > 0) {
      return 'Bạn đã chơi rất tốt!';
    } else {
      return 'Rất tiếc! Hãy thử lại lần sau.';
    }
  };

  // Hiển thị thông báo cập nhật milestone
  const getMilestoneMessage = () => {
    if (isUpdating) {
      return 'Đang cập nhật tiến trình...';
    } else if (updateSuccess) {
      if (isCompleted) {
        return 'Chúc mừng! Bạn đã hoàn thành chặng này!';
      } else {
        return 'Tiến trình của bạn đã được lưu.';
      }
    } else {
      return 'Không thể cập nhật tiến trình. Vui lòng thử lại sau.';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Animatable.View
        animation="fadeIn"
        duration={1000}
        style={styles.content}
      >
        <Text style={styles.titleText}>Kết quả</Text>

        <Animatable.View
          animation="bounceIn"
          delay={500}
          duration={1500}
          style={styles.resultContainer}
        >
          <Image
            source={require('./assets/LogoOwl.png')}
            style={styles.mascotImage}
            resizeMode="contain"
          />

          <Text style={styles.messageText}>{getResultMessage()}</Text>

          <Animatable.View
            animation="pulse"
            iterationCount="infinite"
            duration={2000}
            style={styles.moneyContainer}
          >
            <Text style={styles.moneyText}>
              {formatMoney(gameState.currentMoney)}
            </Text>
          </Animatable.View>

          {gameState.highestScore > 0 && (
            <Text style={styles.highScoreText}>
              Điểm cao nhất: {formatMoney(gameState.highestScore)}
            </Text>
          )}

          {/* Hiển thị thông báo cập nhật milestone */}
          <View style={styles.milestoneContainer}>
            {isUpdating ? (
              <ActivityIndicator size="small" color="#A5B4FC" />
            ) : (
              <Text style={styles.milestoneText}>{getMilestoneMessage()}</Text>
            )}
          </View>
        </Animatable.View>

        <Animatable.View
          animation="fadeInUp"
          delay={1500}
          duration={1000}
        >
          <TouchableOpacity
            style={styles.playAgainButton}
            onPress={handlePlayAgain}
          >
            <Text style={styles.playAgainButtonText}>Chơi lại</Text>
          </TouchableOpacity>
        </Animatable.View>
      </Animatable.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0F172A',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  titleText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFD700',
    marginBottom: 40,
    fontFamily: 'BagelFatOne-Regular',
  },
  resultContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(30, 58, 138, 0.8)',
    padding: 24,
    borderRadius: 16,
    width: width - 60,
    borderWidth: 2,
    borderColor: '#4C6EF5',
  },
  mascotImage: {
    width: 120,
    height: 120,
    marginBottom: 20,
  },
  messageText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 20,
  },
  moneyContainer: {
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#FFD700',
    marginBottom: 16,
  },
  moneyText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFD700',
  },
  highScoreText: {
    fontSize: 16,
    color: '#A5B4FC',
  },
  milestoneContainer: {
    marginTop: 16,
    alignItems: 'center',
    minHeight: 30,
  },
  milestoneText: {
    fontSize: 14,
    color: '#A5B4FC',
    textAlign: 'center',
  },
  playAgainButton: {
    backgroundColor: '#FF4D6D',
    paddingVertical: 15,
    paddingHorizontal: 60,
    borderRadius: 30,
    marginTop: 40,
    borderWidth: 3,
    borderColor: '#FF7A8F',
  },
  playAgainButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: 'BagelFatOne-Regular',
  },
});

export default ResultALTP;

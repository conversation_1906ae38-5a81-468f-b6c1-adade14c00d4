/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {View, Text, FlatList} from 'react-native';
import {SkeletonPlaceCard} from '../card/defaultImage';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {AppButton} from 'wini-mobile-components';
import DefaultBanner from '../card/infor';
import {SocialDA} from '../da';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
  isRefresh?: boolean;
  setRefresh?: (vl: boolean) => void;
}
export default function ByNewCategory(props: Props) {
  const [data, setData] = useState<Array<any>>([]);

  const [isLoading, setLoading] = useState(true);
  const socialDA = new SocialDA();
  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    setLoading(true);
    if (props.setRefresh) {
      props.setRefresh(true);
    }
    const result = await socialDA.getTopicNewsFeed();
    if (result) {
      if (result.data.length > 0) {
        var lst = await Promise.all(
          result.data.map(async (item: any) => ({
            ...item,
            Description: `${await socialDA.totalPostInTopic(item.Id)} post`,
          })),
        );
        setData(lst);
      }
      setLoading(false);
      if (props.setRefresh) props.setRefresh(false);
    }
  };

  return (
    <View
      style={{
        height: props.horizontal ? 180 : undefined,
      }}>
      {props.titleList ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            flexDirection: 'row',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}>
          <Text
            style={{
              ...TypoSkin.heading5,
              color: ColorThemes.light.Neutral_Text_Color_Title,
            }}>
            {props.titleList}
          </Text>
          {props.isSeeMore ? (
            <AppButton
              title={'See more'}
              containerStyle={{
                justifyContent: 'flex-start',
                alignSelf: 'baseline',
              }}
              backgroundColor={'transparent'}
              textStyle={TypoSkin.buttonText3}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={() => {}}
              textColor={ColorThemes.light.Info_Color_Main}
            />
          ) : null}
        </View>
      ) : null}
      <FlatList
        data={data}
        nestedScrollEnabled
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false} //
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        renderItem={({item, index}) => {
          return (
            <DefaultBanner
              key={index}
              flexDirection="default"
              containerStyle={{width: 180, height: 180}}
              data={item}
            />
          );
        }}
        style={{width: '100%', height: '100%', paddingHorizontal: 16}}
        keyExtractor={item => item.Id.toString()}
        horizontal={props.horizontal}
        ListEmptyComponent={() => {
          if (isLoading) {
            return <SkeletonPlaceCard />;
          }
          return <Text style={{color: '#000000'}}>Không có dữ liệu</Text>;
        }}
      />
    </View>
  );
}

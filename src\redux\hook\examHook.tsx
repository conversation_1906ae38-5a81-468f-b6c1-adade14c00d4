import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '../store/store';
import {useEffect} from 'react';
import {ExamActions} from '../reducers/examReducer';

export function useExamData(examId?: string) {
  const dispatch: AppDispatch = useDispatch();
  const examInfor = useSelector((state: RootState) => state.exam.examInfor);
  const listQuestion = useSelector(
    (state: RootState) => state.exam.listQuestion,
  );
  const loading = useSelector((state: RootState) => state.exam.loading);
  const error = useSelector((state: RootState) => state.exam.error);
  useEffect(() => {
    if (examId) {
      dispatch(ExamActions.getExam(examId));
    }
  }, [dispatch, examId]);
  return {examInfor, loading, error, listQuestion};
}

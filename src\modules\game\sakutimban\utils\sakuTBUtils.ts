import { shuffleArray } from '../../../../utils/arrayUtils';
import {
  GameQuizQuestionAPI,
  OptionItem,
  CorrectPair,
  SakuTBItem,
  SakuTBQuestion,
  ParsedOptions,
  ParsedCorrectAnswers,
  SakuTBError,
} from '../types/sakuTBTypes';

/**
 * Shuffle array function
 */

/**
 * Parse GameAnswer array thành leftItems và rightItems
 * @param answers Array of GameAnswer objects
 * @returns ParsedOptions
 */
export const parseAnswers = (answers: any[]): ParsedOptions => {
  try {
    if (!Array.isArray(answers) || answers.length === 0) {
      throw new Error(
        `Invalid answers format. Expected array with items, got ${answers.length}`,
      );
    }

    const leftItems: OptionItem[] = [];
    const rightItems: OptionItem[] = [];

    answers.forEach(answer => {
      const item: OptionItem = {
        id: answer.Id || `answer-${answer.Sort}`,
        text: answer.Name || '',
        type: detectItemType(answer.Name || ''),
        index: answer.Sort || 0,
        sort: answer.Sort || 0,
      };

      // IsResult = true là left item, false là right item
      if (answer.IsResult === true) {
        leftItems.push(item);
      } else if (answer.IsResult !== true) {
        rightItems.push(item);
      }
    });

    // Sort theo Sort field để đảm bảo thứ tự
    leftItems.sort((a, b) => (a.sort || 0) - (b.sort || 0));
    rightItems.sort((a, b) => (a.sort || 0) - (b.sort || 0));

    return {leftItems, rightItems};
  } catch (error) {
    console.error('[parseAnswers] Error parsing answers:', error);
    throw new Error(
      `Failed to parse answers: ${(error as Error).message || 'Unknown error'}`,
    );
  }
};

/**
 * Legacy function - kept for backward compatibility
 * @deprecated Use parseAnswers instead
 */
export const parseOptions = (optionsJson: string): ParsedOptions => {
  try {
    const options: string[] = JSON.parse(optionsJson);

    if (!Array.isArray(options) || options.length !== 10) {
      throw new Error(
        `Invalid options format. Expected 10 items, got ${options.length}`,
      );
    }

    const leftItems: OptionItem[] = [];
    const rightItems: OptionItem[] = [];

    options.forEach((option, index) => {
      const item: OptionItem = {
        id: `option-${index + 1}`,
        text: option.trim(),
        type: detectItemType(option),
        index: index + 1,
      };

      // 5 items đầu (index 1-5) là bên trái
      if (index < 5) {
        leftItems.push(item);
      } else {
        // 5 items còn lại (index 6-10) là bên phải
        rightItems.push(item);
      }
    });

    return {leftItems, rightItems};
  } catch (error) {
    console.error('[parseOptions] Error parsing options:', error);
    throw new Error(
      `Failed to parse options: ${(error as Error).message || 'Unknown error'}`,
    );
  }
};

/**
 * Tạo correct pairs từ GameAnswer array dựa trên Sort field
 * @param answers Array of GameAnswer objects
 * @returns ParsedCorrectAnswers
 */
export const createCorrectPairsFromAnswers = (
  answers: any[],
): ParsedCorrectAnswers => {
  try {
    if (!Array.isArray(answers) || answers.length === 0) {
      throw new Error(
        `Invalid answers format. Expected array with items, got ${answers.length}`,
      );
    }

    const leftItems = answers.filter(a => a.IsResult === true);
    const rightItems = answers.filter(a => a.IsResult !== true);

    if (leftItems.length === 0 || rightItems.length === 0) {
      throw new Error('No left or right items found in answers');
    }

    const pairs: CorrectPair[] = [];

    // Tạo pairs dựa trên Sort field
    leftItems.forEach((leftItem, index) => {
      const rightItem = rightItems.find(r => r.Sort === leftItem.Sort);

      if (rightItem) {
        pairs.push({
          leftIndex: leftItem.Sort || index + 1,
          rightIndex: rightItem.Sort || index + 1,
          matchId: `match-${leftItem.Sort || index + 1}`,
        });
      }
    });

    if (pairs.length === 0) {
      throw new Error('No matching pairs found based on Sort field');
    }

    return {pairs};
  } catch (error) {
    console.error(
      '[createCorrectPairsFromAnswers] Error creating correct pairs:',
      error,
    );
    throw new Error(
      `Failed to create correct pairs: ${
        (error as Error).message || 'Unknown error'
      }`,
    );
  }
};

/**
 * Legacy function - Parse CorrectAnswerIndex JSON string thành array of CorrectPair
 * @param correctAnswerJson JSON string dạng ["1,6","2,7","3,8","4,9","5,10"]
 * @returns ParsedCorrectAnswers
 * @deprecated Use createCorrectPairsFromAnswers instead
 */
export const parseCorrectAnswers = (
  correctAnswerJson: string,
): ParsedCorrectAnswers => {
  try {
    const correctAnswers: string[] = JSON.parse(correctAnswerJson);

    if (!Array.isArray(correctAnswers) || correctAnswers.length !== 5) {
      throw new Error(
        `Invalid correct answers format. Expected 5 pairs, got ${correctAnswers.length}`,
      );
    }

    const pairs: CorrectPair[] = correctAnswers.map((pair, index) => {
      const [leftIndex, rightIndex] = pair.split(',').map(Number);

      // Validate indices
      if (isNaN(leftIndex) || isNaN(rightIndex)) {
        throw new Error(`Invalid pair format: ${pair}`);
      }

      if (leftIndex < 1 || leftIndex > 5) {
        throw new Error(`Invalid left index: ${leftIndex}. Must be 1-5`);
      }

      if (rightIndex < 6 || rightIndex > 10) {
        throw new Error(`Invalid right index: ${rightIndex}. Must be 6-10`);
      }

      return {
        leftIndex,
        rightIndex,
        matchId: `match-${index + 1}`,
      };
    });

    return {pairs};
  } catch (error) {
    console.error(
      '[parseCorrectAnswers] Error parsing correct answers:',
      error,
    );
    throw new Error(
      `Failed to parse correct answers: ${
        (error as Error).message || 'Unknown error'
      }`,
    );
  }
};

/**
 * Detect item type based on content
 * @param content Item content
 * @returns Item type
 */
export const detectItemType = (content: string): 'text' | 'audio' | 'image' => {
  const trimmedContent = content.trim().toLowerCase();

  // Check for audio URLs
  if (
    trimmedContent.includes('.mp3') ||
    trimmedContent.includes('.wav') ||
    trimmedContent.includes('.m4a') ||
    trimmedContent.includes('audio')
  ) {
    return 'audio';
  }

  // Check for image URLs
  if (
    trimmedContent.includes('.jpg') ||
    trimmedContent.includes('.jpeg') ||
    trimmedContent.includes('.png') ||
    trimmedContent.includes('.gif') ||
    trimmedContent.includes('.webp') ||
    trimmedContent.includes('image')
  ) {
    return 'image';
  }

  // Default to text
  return 'text';
};

/**
 * Transform GameQuizQuestionAPI thành SakuTBQuestion (sử dụng GameAnswer)
 * @param rawQuestion Raw question from API (có field Answers)
 * @returns SakuTBQuestion
 */
export const transformQuestion = (
  rawQuestion: GameQuizQuestionAPI,
): SakuTBQuestion => {
  try {
    let leftItems: OptionItem[] = [];
    let rightItems: OptionItem[] = [];
    let pairs: CorrectPair[] = [];

    // Kiểm tra xem có Answers (logic mới) hay Options (logic cũ)
    if (
      (rawQuestion as any).Answers &&
      Array.isArray((rawQuestion as any).Answers)
    ) {
      // Logic mới: sử dụng GameAnswer
      const answers = (rawQuestion as any).Answers;
      const parsedAnswers = parseAnswers(answers);
      leftItems = parsedAnswers.leftItems;
      rightItems = parsedAnswers.rightItems;
      const parsedPairs = createCorrectPairsFromAnswers(answers);
      pairs = parsedPairs.pairs;
    } else {
      // Logic cũ: fallback cho backward compatibility
      const parsedOptions = parseOptions(rawQuestion.Options);
      leftItems = parsedOptions.leftItems;
      rightItems = parsedOptions.rightItems;

      const parsedPairs = parseCorrectAnswers(rawQuestion.CorrectAnswerIndex);
      pairs = parsedPairs.pairs;
    }

    // Transform to SakuTBItems
    const leftSakuItems: SakuTBItem[] = leftItems.map(item => {
      // Find matching pair for this left item
      const matchingPair = pairs.find(
        pair => pair.leftIndex === item.index || pair.leftIndex === item.sort,
      );

      return {
        id: `left-${rawQuestion.Id}-${item.index || item.sort}`,
        text: item.text,
        type: item.type,
        matchId: matchingPair
          ? matchingPair.matchId
          : `no-match-${item.index || item.sort}`,
        side: 'left',
        originalIndex: item.index || item.sort || 0,
      };
    });

    const rightSakuItems: SakuTBItem[] = rightItems.map(item => {
      // Find matching pair for this right item
      const matchingPair = pairs.find(
        pair => pair.rightIndex === item.index || pair.rightIndex === item.sort,
      );

      return {
        id: `right-${rawQuestion.Id}-${item.index || item.sort}`,
        text: item.text,
        type: item.type,
        matchId: matchingPair
          ? matchingPair.matchId
          : `no-match-${item.index || item.sort}`,
        side: 'right',
        originalIndex: item.index || item.sort || 0,
      };
    });

    return {
      id: rawQuestion.Id,
      content: rawQuestion.Name,
      leftItems: shuffleArray(leftSakuItems),
      rightItems: shuffleArray(rightSakuItems),
      correctPairs: pairs,
    };
  } catch (error) {
    console.error('[transformQuestion] Error transforming question:', error);
    throw new Error(
      `Failed to transform question ${rawQuestion.Id}: ${
        (error as Error).message || 'Unknown error'
      }`,
    );
  }
};

/**
 * Transform multiple questions
 * @param rawQuestions Array of raw questions
 * @returns Array of transformed questions
 */
export const transformQuestions = (
  rawQuestions: GameQuizQuestionAPI[],
): SakuTBQuestion[] => {
  const transformedQuestions: SakuTBQuestion[] = [];
  const errors: string[] = [];

  rawQuestions.forEach((rawQuestion, index) => {
    try {
      const transformed = transformQuestion(rawQuestion);
      transformedQuestions.push(transformed);
    } catch (error) {
      console.error(
        `[transformQuestions] Error transforming question ${index + 1}:`,
        error,
      );
      errors.push(
        `Question ${index + 1}: ${(error as Error).message || 'Unknown error'}`,
      );
    }
  });

  if (errors.length > 0) {
    console.warn(
      `[transformQuestions] ${errors.length} questions failed to transform:`,
      errors,
    );
  }

  console.log(
    `[transformQuestions] Successfully transformed ${transformedQuestions.length}/${rawQuestions.length} questions`,
  );
  return transformedQuestions;
};

/**
 * Validate transformed question (hỗ trợ cả logic cũ và mới)
 * @param question Transformed question
 * @returns boolean
 */
export const validateTransformedQuestion = (
  question: SakuTBQuestion,
): boolean => {
  try {
    // Check basic structure
    if (
      !question.id ||
      !question.content ||
      !question.leftItems ||
      !question.rightItems ||
      !question.correctPairs
    ) {
      console.warn(
        `[validateTransformedQuestion] Question ${question.id} missing basic structure`,
      );
      return false;
    }

    // Check items count - flexible cho logic mới
    if (question.leftItems.length === 0 || question.rightItems.length === 0) {
      console.warn(
        `[validateTransformedQuestion] Question ${question.id} has no left or right items`,
      );
      return false;
    }

    // Check correct pairs count - flexible cho logic mới
    if (question.correctPairs.length === 0) {
      console.warn(
        `[validateTransformedQuestion] Question ${question.id} has no correct pairs`,
      );
      return false;
    }

    // Check số lượng left và right items phải bằng nhau
    if (question.leftItems.length !== question.rightItems.length) {
      console.warn(
        `[validateTransformedQuestion] Question ${question.id} has mismatched left/right items count`,
      );
      return false;
    }

    // Check số lượng correct pairs phải bằng số lượng items
    if (question.correctPairs.length !== question.leftItems.length) {
      console.warn(
        `[validateTransformedQuestion] Question ${question.id} has mismatched pairs count`,
      );
      return false;
    }

    // Check all items have valid matchIds
    const allItems = [...question.leftItems, ...question.rightItems];
    const hasInvalidMatchId = allItems.some(
      item => !item.matchId || item.matchId.startsWith('no-match'),
    );

    if (hasInvalidMatchId) {
      console.warn(
        `[validateTransformedQuestion] Question ${question.id} has invalid matchIds`,
      );
      return false;
    }

    // Check all items have valid content
    const hasEmptyContent = allItems.some(
      item => !item.text || item.text.trim() === '',
    );

    if (hasEmptyContent) {
      console.warn(
        `[validateTransformedQuestion] Question ${question.id} has empty content`,
      );
      return false;
    }

    return true;
  } catch (error) {
    console.error('[validateTransformedQuestion] Validation error:', error);
    return false;
  }
};

/**
 * Generate unique ID
 */
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

/**
 * Format time display
 */
export const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`;
};

/**
 * Create error object
 */
export const createError = (
  type: SakuTBError['type'],
  message: string,
  details?: any,
): SakuTBError => {
  return {type, message, details};
};

/**
 * Log question structure for debugging
 */
export const logQuestionStructure = (question: SakuTBQuestion): void => {
  console.log(`[Question ${question.id}] Content: ${question.content}`);
  console.log(
    'Left items:',
    question.leftItems.map(
      item => `${item.originalIndex}: ${item.text} (${item.matchId})`,
    ),
  );
  console.log(
    'Right items:',
    question.rightItems.map(
      item => `${item.originalIndex}: ${item.text} (${item.matchId})`,
    ),
  );
  console.log(
    'Correct pairs:',
    question.correctPairs.map(
      pair => `${pair.leftIndex} -> ${pair.rightIndex} (${pair.matchId})`,
    ),
  );
};

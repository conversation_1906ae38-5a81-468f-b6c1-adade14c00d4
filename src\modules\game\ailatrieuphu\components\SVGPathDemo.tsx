import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { useSVGPath, useBirdAnimation, usePathTrail } from '../hooks/useSVGPath';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const SVGPathDemo: React.FC = () => {
  const [selectedMilestone, setSelectedMilestone] = useState(1);
  const [completedMilestones, setCompletedMilestones] = useState([1, 2, 3]);

  // Container dimensions (giả lập game area)
  const containerDimensions = {
    width: screenWidth,
    height: screenHeight * 0.7, // 70% màn hình cho game area
  };

  // SVG Path hooks
  const { milestonePositions, pathTrail } = useSVGPath(containerDimensions);
  const { completedTrail } = usePathTrail(completedMilestones, containerDimensions);
  
  // Bird animation từ milestone 3 đến 4
  const { birdPosition, startAnimation, isAnimating } = useBirdAnimation(
    3, 4, 2000, containerDimensions
  );

  const handleMilestonePress = (id: number) => {
    setSelectedMilestone(id);
    if (!completedMilestones.includes(id)) {
      setCompletedMilestones([...completedMilestones, id]);
    }
  };

  const handleAnimateBird = () => {
    startAnimation();
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>SVG Path Demo - Ai là triệu phú</Text>
      
      {/* Game Area */}
      <View style={[styles.gameArea, containerDimensions]}>
        {/* Full Path Trail (màu nhạt) */}
        {pathTrail.map((point, index) => (
          <View
            key={`full-${index}`}
            style={[
              styles.fullTrailPoint,
              {
                left: point.x - 1,
                top: point.y - 1,
              }
            ]}
          />
        ))}

        {/* Completed Path Trail (màu đậm) */}
        {completedTrail.map((point, index) => (
          <View
            key={`completed-${index}`}
            style={[
              styles.completedTrailPoint,
              {
                left: point.x - 2,
                top: point.y - 2,
              }
            ]}
          />
        ))}

        {/* Milestones */}
        {milestonePositions.map((milestone) => {
          const isCompleted = completedMilestones.includes(milestone.id);
          const isSelected = selectedMilestone === milestone.id;
          
          return (
            <TouchableOpacity
              key={milestone.id}
              style={[
                styles.milestone,
                {
                  left: milestone.x - 25,
                  top: milestone.y - 25,
                },
                isCompleted && styles.completedMilestone,
                isSelected && styles.selectedMilestone,
              ]}
              onPress={() => handleMilestonePress(milestone.id)}
            >
              <Text style={styles.milestoneText}>{milestone.id}</Text>
              <Text style={styles.milestoneName}>{milestone.name}</Text>
            </TouchableOpacity>
          );
        })}

        {/* Animated Bird */}
        {isAnimating && (
          <View
            style={[
              styles.bird,
              {
                left: birdPosition.x - 15,
                top: birdPosition.y - 15,
                transform: [{ rotate: `${birdPosition.angle}rad` }],
              }
            ]}
          >
            <Text style={styles.birdEmoji}>🐦</Text>
          </View>
        )}
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        <TouchableOpacity style={styles.button} onPress={handleAnimateBird}>
          <Text style={styles.buttonText}>
            {isAnimating ? 'Animating...' : 'Animate Bird (3→4)'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.button} 
          onPress={() => setCompletedMilestones([])}
        >
          <Text style={styles.buttonText}>Reset Trail</Text>
        </TouchableOpacity>
      </View>

      {/* Debug Info */}
      <View style={styles.debugInfo}>
        <Text style={styles.debugText}>
          Container: {containerDimensions.width}x{containerDimensions.height}
        </Text>
        <Text style={styles.debugText}>
          Milestones: {milestonePositions.length}
        </Text>
        <Text style={styles.debugText}>
          Trail Points: {pathTrail.length}
        </Text>
        <Text style={styles.debugText}>
          Completed: {completedMilestones.join(', ')}
        </Text>
        <Text style={styles.debugText}>
          Selected: {selectedMilestone}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    padding: 16,
    backgroundColor: '#112164',
    color: '#fff',
  },
  gameArea: {
    backgroundColor: '#e8f5e8',
    margin: 16,
    borderRadius: 8,
    position: 'relative',
    borderWidth: 2,
    borderColor: '#112164',
  },
  fullTrailPoint: {
    position: 'absolute',
    width: 2,
    height: 2,
    borderRadius: 1,
    backgroundColor: '#ddd',
  },
  completedTrailPoint: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#4CAF50',
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 3,
  },
  milestone: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#9E9E9E',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#616161',
  },
  completedMilestone: {
    backgroundColor: '#4CAF50',
    borderColor: '#2E7D32',
  },
  selectedMilestone: {
    backgroundColor: '#FF9800',
    borderColor: '#F57C00',
    transform: [{ scale: 1.1 }],
  },
  milestoneText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  milestoneName: {
    fontSize: 8,
    color: '#fff',
    textAlign: 'center',
    marginTop: 2,
  },
  bird: {
    position: 'absolute',
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  birdEmoji: {
    fontSize: 20,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
  },
  button: {
    backgroundColor: '#112164',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  debugInfo: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 12,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
});

export default SVGPathDemo;

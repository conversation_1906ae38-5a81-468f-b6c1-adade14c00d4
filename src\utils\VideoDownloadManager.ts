import RNFS from 'react-native-fs';
import { Platform } from 'react-native';
import { saveObjToAsyncStorage, getObjToAsyncStorage } from './AsyncStorage';
import { showSnackbar, ComponentStatus } from 'wini-mobile-components';

export interface SavedVideo {
  id: string;
  videoUrl: string;
  localPath: string;
  lessonName: string;
  lessonId: string;
  courseId: string;
  videoName: string;
  downloadDate: string;
  fileSize: number;
  duration?: number;
  downloadId?: string;
  downloadStatus: 'downloading' | 'completed' | 'paused' | 'failed' | 'pending';
  progress: number; // 0-100
  thumbnailPath?: string;
}

export interface DownloadProgress {
  id: string;
  progress: number;
  bytesWritten: number;
  totalBytes: number;
  status: 'downloading' | 'completed' | 'paused' | 'failed';
}

class VideoDownloadManager {
  private static instance: VideoDownloadManager;
  private downloadTasks: Map<string, any> = new Map();
  private progressCallbacks: Map<string, (progress: DownloadProgress) => void> = new Map();
  private readonly STORAGE_KEY = 'SAVED_VIDEOS';
  private readonly DOWNLOAD_DIR = Platform.OS === 'ios'
    ? RNFS.DocumentDirectoryPath + '/SavedVideos'
    : RNFS.DownloadDirectoryPath + '/SavedVideos';

  private constructor() {
    this.initializeDownloadDirectory();
  }

  public static getInstance(): VideoDownloadManager {
    if (!VideoDownloadManager.instance) {
      VideoDownloadManager.instance = new VideoDownloadManager();
    }
    return VideoDownloadManager.instance;
  }

  private async initializeDownloadDirectory(): Promise<void> {
    try {
      const exists = await RNFS.exists(this.DOWNLOAD_DIR);
      if (!exists) {
        await RNFS.mkdir(this.DOWNLOAD_DIR);
        console.log('Created download directory:', this.DOWNLOAD_DIR);
      }
    } catch (error) {
      console.error('Error creating download directory:', error);
    }
  }

  private generateVideoId(lessonId: string, videoUrl: string): string {
    return `${lessonId}_${Date.now()}`;
  }

  private getVideoFileName(videoUrl: string, videoId: string): string {
    const extension = videoUrl.split('.').pop()?.split('?')[0] || 'mp4';
    return `video_${videoId}.${extension}`;
  }

  private async getSavedVideos(): Promise<SavedVideo[]> {
    try {
      const savedVideos = await getObjToAsyncStorage(this.STORAGE_KEY);
      return savedVideos || [];
    } catch (error) {
      console.error('Error getting saved videos:', error);
      return [];
    }
  }

  private async saveSavedVideos(videos: SavedVideo[]): Promise<void> {
    try {
      await saveObjToAsyncStorage(this.STORAGE_KEY, videos);
    } catch (error) {
      console.error('Error saving videos:', error);
    }
  }

  public async startDownload(
    videoData: {
      videoUrl: string;
      lessonName: string;
      lessonId: string;
      courseId: string;
      videoName: string;
    },
    onProgress?: (progress: DownloadProgress) => void,
    onComplete?: (savedVideo: SavedVideo) => void,
    onError?: (error: string) => void
  ): Promise<string | null> {
    try {
      // Ensure download directory exists before starting download
      await this.initializeDownloadDirectory();

      const videoId = this.generateVideoId(videoData.lessonId, videoData.videoUrl);
      const fileName = this.getVideoFileName(videoData.videoUrl, videoId);
      const localPath = `${this.DOWNLOAD_DIR}/${fileName}`;

      // Check if video already exists
      const savedVideos = await this.getSavedVideos();
      const existingVideo = savedVideos.find(v =>
        v.lessonId === videoData.lessonId && v.videoUrl === videoData.videoUrl
      );

      if (existingVideo && existingVideo.downloadStatus === 'completed') {
        showSnackbar({
          message: 'Video đã được tải xuống trước đó',
          status: ComponentStatus.WARNING,
        });
        return existingVideo.id;
      }

      // Create saved video entry
      const savedVideo: SavedVideo = {
        id: videoId,
        videoUrl: videoData.videoUrl,
        localPath,
        lessonName: videoData.lessonName,
        lessonId: videoData.lessonId,
        courseId: videoData.courseId,
        videoName: videoData.videoName,
        downloadDate: new Date().toISOString(),
        fileSize: 0,
        downloadStatus: 'downloading',
        progress: 0,
      };

      // Save to storage
      const updatedVideos = [...savedVideos.filter(v => v.id !== videoId), savedVideo];
      await this.saveSavedVideos(updatedVideos);

      if (onProgress) {
        this.progressCallbacks.set(videoId, onProgress);
      }

      // Start download using RNFS
      const downloadPromise = RNFS.downloadFile({
        fromUrl: videoData.videoUrl,
        toFile: localPath,
        progress: (res) => {
          const progressPercent = Math.round((res.bytesWritten / res.contentLength) * 100);

          const progress: DownloadProgress = {
            id: videoId,
            progress: progressPercent,
            bytesWritten: res.bytesWritten,
            totalBytes: res.contentLength,
            status: 'downloading',
          };

          savedVideo.progress = progressPercent;
          savedVideo.fileSize = res.contentLength;
          this.updateSavedVideo(savedVideo);

          const callback = this.progressCallbacks.get(videoId);
          if (callback) {
            callback(progress);
          }
        },
      });

      this.downloadTasks.set(videoId, downloadPromise);

      // Handle download completion
      downloadPromise.promise
        .then((result) => {
          console.log(`Download completed for ${videoId}`, result);
          savedVideo.downloadStatus = 'completed';
          savedVideo.progress = 100;
          this.updateSavedVideo(savedVideo);

          this.downloadTasks.delete(videoId);
          this.progressCallbacks.delete(videoId);

          showSnackbar({
            message: `Tải video "${videoData.videoName}" thành công!`,
            status: ComponentStatus.SUCCSESS,
          });

          if (onComplete) {
            onComplete(savedVideo);
          }
        })
        .catch((error) => {
          console.error(`Download failed for ${videoId}:`, error);
          savedVideo.downloadStatus = 'failed';
          this.updateSavedVideo(savedVideo);

          this.downloadTasks.delete(videoId);
          this.progressCallbacks.delete(videoId);

          const errorMessage = `Lỗi tải video: ${error.message || error}`;
          showSnackbar({
            message: errorMessage,
            status: ComponentStatus.ERROR,
          });

          if (onError) {
            onError(errorMessage);
          }
        });

      return videoId;
    } catch (error) {
      console.error('Error starting download:', error);
      const errorMessage = 'Không thể bắt đầu tải video';
      showSnackbar({
        message: errorMessage,
        status: ComponentStatus.ERROR,
      });

      if (onError) {
        onError(errorMessage);
      }
      return null;
    }
  }

  private async updateSavedVideo(updatedVideo: SavedVideo): Promise<void> {
    try {
      const savedVideos = await this.getSavedVideos();
      const index = savedVideos.findIndex(v => v.id === updatedVideo.id);
      if (index !== -1) {
        savedVideos[index] = updatedVideo;
        await this.saveSavedVideos(savedVideos);
      }
    } catch (error) {
      console.error('Error updating saved video:', error);
    }
  }

  public async pauseDownload(videoId: string): Promise<boolean> {
    try {
      const task = this.downloadTasks.get(videoId);
      if (task && task.stop) {
        task.stop();

        const savedVideos = await this.getSavedVideos();
        const video = savedVideos.find(v => v.id === videoId);
        if (video) {
          video.downloadStatus = 'paused';
          await this.updateSavedVideo(video);
        }

        return true;
      }
      return false;
    } catch (error) {
      console.error('Error pausing download:', error);
      return false;
    }
  }

  public async resumeDownload(videoId: string): Promise<boolean> {
    try {
      // For RNFS, we need to restart the download
      const savedVideos = await this.getSavedVideos();
      const video = savedVideos.find(v => v.id === videoId);
      if (video && video.downloadStatus === 'paused') {
        // Restart download from where it left off
        return await this.startDownload({
          videoUrl: video.videoUrl,
          lessonName: video.lessonName,
          lessonId: video.lessonId,
          courseId: video.courseId,
          videoName: video.videoName,
        }) !== null;
      }
      return false;
    } catch (error) {
      console.error('Error resuming download:', error);
      return false;
    }
  }

  public async cancelDownload(videoId: string): Promise<boolean> {
    try {
      const task = this.downloadTasks.get(videoId);
      if (task && task.stop) {
        task.stop();
        this.downloadTasks.delete(videoId);
        this.progressCallbacks.delete(videoId);
      }

      // Remove from saved videos and delete file
      const savedVideos = await this.getSavedVideos();
      const video = savedVideos.find(v => v.id === videoId);
      if (video) {
        // Delete file if exists
        const exists = await RNFS.exists(video.localPath);
        if (exists) {
          await RNFS.unlink(video.localPath);
        }

        // Remove from saved videos
        const updatedVideos = savedVideos.filter(v => v.id !== videoId);
        await this.saveSavedVideos(updatedVideos);
      }

      return true;
    } catch (error) {
      console.error('Error canceling download:', error);
      return false;
    }
  }

  public async getAllSavedVideos(): Promise<SavedVideo[]> {
    return await this.getSavedVideos();
  }

  public async getSavedVideosByLesson(lessonId: string): Promise<SavedVideo[]> {
    const savedVideos = await this.getSavedVideos();
    return savedVideos.filter(v => v.lessonId === lessonId);
  }

  public async deleteVideo(videoId: string): Promise<boolean> {
    try {
      const savedVideos = await this.getSavedVideos();
      const video = savedVideos.find(v => v.id === videoId);
      
      if (video) {
        // Cancel download if in progress
        if (video.downloadStatus === 'downloading' || video.downloadStatus === 'paused') {
          await this.cancelDownload(videoId);
        } else {
          // Delete file if exists
          const exists = await RNFS.exists(video.localPath);
          if (exists) {
            await RNFS.unlink(video.localPath);
          }
          
          // Remove from saved videos
          const updatedVideos = savedVideos.filter(v => v.id !== videoId);
          await this.saveSavedVideos(updatedVideos);
        }
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error deleting video:', error);
      return false;
    }
  }

  public async getDownloadProgress(videoId: string): Promise<DownloadProgress | null> {
    try {
      const savedVideos = await this.getSavedVideos();
      const video = savedVideos.find(v => v.id === videoId);
      
      if (video) {
        return {
          id: videoId,
          progress: video.progress,
          bytesWritten: 0, // This would need to be tracked separately
          totalBytes: video.fileSize,
          status: video.downloadStatus as any,
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error getting download progress:', error);
      return null;
    }
  }

  public async getTotalStorageUsed(): Promise<number> {
    try {
      const savedVideos = await this.getSavedVideos();
      let totalSize = 0;
      
      for (const video of savedVideos) {
        if (video.downloadStatus === 'completed') {
          try {
            const stat = await RNFS.stat(video.localPath);
            totalSize += stat.size;
          } catch (error) {
            console.warn(`Could not get size for ${video.localPath}:`, error);
          }
        }
      }
      
      return totalSize;
    } catch (error) {
      console.error('Error calculating total storage:', error);
      return 0;
    }
  }

  public async isVideoDownloaded(lessonId: string, videoUrl: string): Promise<boolean> {
    try {
      const savedVideos = await this.getSavedVideos();
      const video = savedVideos.find(v =>
        v.lessonId === lessonId &&
        v.videoUrl === videoUrl &&
        v.downloadStatus === 'completed'
      );

      if (video) {
        // Verify file still exists
        const exists = await RNFS.exists(video.localPath);
        return exists;
      }

      return false;
    } catch (error) {
      console.error('Error checking if video is downloaded:', error);
      return false;
    }
  }

  /**
   * Clear all download tasks and callbacks (useful after clearing all videos)
   */
  public clearDownloadCache(): void {
    this.downloadTasks.clear();
    this.progressCallbacks.clear();
  }

  /**
   * Clean up invalid records (videos marked as completed but files don't exist)
   */
  public async cleanupInvalidRecords(): Promise<void> {
    try {
      const savedVideos = await this.getSavedVideos();
      const validVideos: SavedVideo[] = [];

      for (const video of savedVideos) {
        if (video.downloadStatus === 'completed') {
          // Check if file still exists
          const exists = await RNFS.exists(video.localPath);
          if (exists) {
            validVideos.push(video);
          } else {
            console.log(`Removing invalid record for deleted video: ${video.videoName}`);
          }
        } else {
          // Keep non-completed videos (downloading, paused, failed)
          validVideos.push(video);
        }
      }

      // Update storage with only valid videos
      if (validVideos.length !== savedVideos.length) {
        await this.saveSavedVideos(validVideos);
      }
    } catch (error) {
      console.error('Error cleaning up invalid records:', error);
    }
  }
}

export default VideoDownloadManager;

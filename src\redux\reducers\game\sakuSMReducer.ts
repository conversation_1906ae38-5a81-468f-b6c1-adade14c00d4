import {createSlice, createAsyncThunk} from '@reduxjs/toolkit';
import {SakuSMDA} from '../../../modules/game/sakusanmoi/da/sakuSMDA';
import {SakuSMQuestion, SakuSMGameConfig} from '../../../modules/game/sakusanmoi/types/sakuSMTypes';

// Giữ lại interface cũ để tương thích
export interface SakuSmAnswer {
  id: number | string;
  text: string;
  isTrue: boolean;
}

interface Question {
  id: number | string;
  question: string;
  hint: string;
  answers: SakuSmAnswer[];
}

const data: Question[] = [
  {
    id: 1,
    question:
      'Tân Tổng thống Ukraine Volodymyr <PERSON> làm nghề gì trước khi nhậm chức?',
    hint: 'Nghề này thường đóng trong các bộ phim hoặc vở kịch hài hước',
    answers: [
      {
        id: 1,
        text: '<PERSON><PERSON> sĩ quyền anh',
        isTrue: false,
      },
      {
        id: 2,
        text: '<PERSON><PERSON><PERSON> viên hài',
        isTrue: true,
      },
      {
        id: 3,
        text: '<PERSON><PERSON><PERSON> sĩ phẫu thuật',
        isTrue: false,
      },
      {
        id: 4,
        text: 'Doanh nhân',
        isTrue: false,
      },
    ],
  },
  {
    id: 2,
    question: 'Đâu là tên một loại đồ chơi dân gian của trẻ em?',
    hint: 'là một loại đồ chơi trẻ em dân gian của Việt Nam, có thể ăn được',
    answers: [
      {
        id: 1,
        text: 'Tò he',
        isTrue: true,
      },
      {
        id: 2,
        text: 'Tò mò',
        isTrue: false,
      },
      {
        id: 3,
        text: 'Tò vò',
        isTrue: false,
      },
      {
        id: 4,
        text: 'Tến tò',
        isTrue: false,
      },
    ],
  },
  {
    id: 3,
    question: 'Có câu: "Mật ngọt chết ..." gì?',
    hint: 'là một loại đồ chơi trẻ em dân gian của Việt Nam, có thể ăn được',
    answers: [
      {
        id: 1,
        text: 'Đàn gà',
        isTrue: false,
      },
      {
        id: 2,
        text: 'Ruồi',
        isTrue: true,
      },
      {
        id: 3,
        text: 'Đàn bò',
        isTrue: false,
      },
      {
        id: 4,
        text: 'Đàn chó',
        isTrue: false,
      },
    ],
  },
  {
    id: 4,
    question: 'Đâu không phải là một tác phẩm của họa sĩ Trần Văn Cẩn?',
    hint: '',
    answers: [
      {
        id: 1,
        text: 'Đôi bạn',
        isTrue: false,
      },
      {
        id: 2,
        text: 'Mẹ',
        isTrue: false,
      },
      {
        id: 3,
        text: 'Em gái tôi',
        isTrue: true,
      },
      {
        id: 4,
        text: 'Em Thuý',
        isTrue: false,
      },
    ],
  },
];

// Async thunk để load game config từ API
export const loadSakuSMGameConfig = createAsyncThunk(
  'sakuSM/loadGameConfig',
  async ({ gameId }: { gameId: string }) => {
    try {
      console.log(`[Redux] Loading SakuSM game config for GameId: ${gameId}`);
      const config = await SakuSMDA.getGameConfig(gameId);
      return config;
    } catch (error) {
      console.error('[Redux] Error loading SakuSM game config:', error);
      throw error;
    }
  }
);

// Async thunk để load questions và answers từ API
export const loadSakuSMQuestions = createAsyncThunk(
  'sakuSM/loadQuestions',
  async ({ gameId, stage, competenceId }: { gameId: string; stage: number; competenceId: string }) => {
    try {
      console.log(`[Redux] Loading SakuSM questions for GameId: ${gameId}, Stage: ${stage}, CompetenceId: ${competenceId}`);

      const sakuSMDA = new SakuSMDA();
      const questions = await sakuSMDA.getQuestionsByGameAndStage(gameId, stage, competenceId);

      if (questions.length === 0) {
        throw new Error('No questions found for this game');
      }

      return questions;
    } catch (error) {
      console.error('[Redux] Error loading SakuSM questions:', error);
      throw error;
    }
  }
);

interface SakuSMState {
  // API Data
  questions: SakuSMQuestion[];
  gameConfig: SakuSMGameConfig | null;

  // Current Game State (giữ tương thích với code cũ)
  listQuestion: Question[];
  currentQuestion: Question;
  questionDone: number;
  totalQuestion: number;

  // API Loading States
  loading: boolean;
  configLoading: boolean;
  error: string | null;
  configError: string | null;
  initialized: boolean;
  configInitialized: boolean;

  // Hint state
  usedHints: string[]; // Array chứa ID của các câu hỏi đã sử dụng hint
}

const initialState: SakuSMState = {
  // API Data
  questions: [],
  gameConfig: null,

  // Current Game State (fallback data)
  listQuestion: data,
  currentQuestion: data[0],
  questionDone: 0,
  totalQuestion: 5,

  // API Loading States
  loading: false,
  configLoading: false,
  error: null,
  configError: null,
  initialized: false,
  configInitialized: false,

  // Hint state
  usedHints: [],
};

export const SakuSMReducer = createSlice({
  name: 'sakuSMReducer',
  initialState,
  reducers: {
    setData(state, action) {
      const { stateName, value } = action.payload;
      (state as any)[stateName] = value;
    },
    startGame(state) {
      if (state.questions.length > 0) {
        // Sử dụng dữ liệu từ API
        state.listQuestion = state.questions as Question[];
        state.currentQuestion = state.questions[0] as Question;
        state.questionDone = 0;
        state.totalQuestion = state.questions.length;
      } else {
        // Fallback về dữ liệu hardcode
        state.listQuestion = data;
        state.currentQuestion = data[0];
        state.questionDone = 0;
        state.totalQuestion = data.length;
      }
      // Reset hint usage
      state.usedHints = [];
    },
    reset(state) {
      state.listQuestion = data;
      state.currentQuestion = data[0];
      state.questionDone = 0;
      state.totalQuestion = 5;
      state.questions = [];
      state.gameConfig = null;
      state.loading = false;
      state.configLoading = false;
      state.error = null;
      state.configError = null;
      state.initialized = false;
      state.configInitialized = false;
      state.usedHints = [];
    },
    // Action để đánh dấu đã sử dụng hint cho câu hỏi
    markHintUsed(state, action) {
      const questionId = action.payload;
      if (!state.usedHints.includes(questionId)) {
        state.usedHints.push(questionId);
      }
    },
  },
  extraReducers: (builder) => {
    // Load Game Config
    builder
      .addCase(loadSakuSMGameConfig.pending, (state) => {
        state.configLoading = true;
        state.configError = null;
      })
      .addCase(loadSakuSMGameConfig.fulfilled, (state, action) => {
        state.configLoading = false;
        state.gameConfig = action.payload;
        state.configInitialized = true;
        state.configError = null;
      })
      .addCase(loadSakuSMGameConfig.rejected, (state, action) => {
        state.configLoading = false;
        state.configError = action.error.message || 'Failed to load game config';
        state.configInitialized = false;
      })

    // Load Questions
    builder
      .addCase(loadSakuSMQuestions.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadSakuSMQuestions.fulfilled, (state, action) => {
        state.loading = false;
        state.questions = action.payload;
        state.initialized = true;
        state.error = null;
      })
      .addCase(loadSakuSMQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to load questions';
        state.initialized = false;
      });
  },
});

export const {setData, reset, startGame, markHintUsed} = SakuSMReducer.actions;

export default SakuSMReducer.reducer;

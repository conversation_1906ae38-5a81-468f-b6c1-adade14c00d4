# Noto Sans JP Font Integration

This document explains how to use the Noto Sans JP fonts in your React Native application.

## Font Files

The following Noto Sans JP font files are available in the `Noto_Sans_JP` folder:

### Static Font Files
- `NotoSansJP-Thin.ttf` (100 weight)
- `NotoSansJP-ExtraLight.ttf` (200 weight)
- `NotoSansJP-Light.ttf` (300 weight)
- `NotoSansJP-Regular.ttf` (400 weight)
- `NotoSansJP-Medium.ttf` (500 weight)
- `NotoSansJP-SemiBold.ttf` (600 weight)
- `NotoSansJP-Bold.ttf` (700 weight)
- `NotoSansJP-ExtraBold.ttf` (800 weight)
- `NotoSansJP-Black.ttf` (900 weight)

### Variable Font File
- `NotoSansJP-VariableFont_wght.ttf` (supports weights 100-900)

## Configuration

### 1. React Native Configuration
The fonts are registered in `react-native.config.js`:

```javascript
module.exports = {
  project: {
    ios: {},
    android: {},
  },
  assets: ['./src/assets/fonts/'],
};
```

### 2. Typography Styles
Pre-configured typography styles are available in `src/assets/skin/typography.tsx` under `TypoSkin.notoSansJP`.

## Usage

### Basic Usage
```typescript
import { Text } from 'react-native';
import { TypoSkin } from '../assets/skin/typography';

// Using predefined styles
<Text style={TypoSkin.notoSansJP.heading1}>見出しテキスト</Text>
<Text style={TypoSkin.notoSansJP.body1}>本文テキスト</Text>
<Text style={TypoSkin.notoSansJP.caption}>キャプション</Text>
```

### Custom Usage
```typescript
import { Text, StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  customText: {
    fontFamily: 'NotoSansJP-Regular',
    fontSize: 16,
    lineHeight: 24,
    color: '#333333',
  },
  boldText: {
    fontFamily: 'NotoSansJP-Bold',
    fontSize: 18,
    fontWeight: '700',
  },
});

<Text style={styles.customText}>カスタムスタイル</Text>
<Text style={styles.boldText}>太字テキスト</Text>
```

## Available Typography Styles

### Regular Styles
- `TypoSkin.notoSansJP.regular0` - 10px
- `TypoSkin.notoSansJP.regular1` - 12px
- `TypoSkin.notoSansJP.regular2` - 14px
- `TypoSkin.notoSansJP.regular3` - 16px
- `TypoSkin.notoSansJP.regular4` - 20px

### Medium Styles
- `TypoSkin.notoSansJP.medium1` - 12px
- `TypoSkin.notoSansJP.medium2` - 14px
- `TypoSkin.notoSansJP.medium3` - 16px
- `TypoSkin.notoSansJP.medium4` - 20px

### Bold Styles
- `TypoSkin.notoSansJP.bold1` - 12px
- `TypoSkin.notoSansJP.bold2` - 14px
- `TypoSkin.notoSansJP.bold3` - 16px
- `TypoSkin.notoSansJP.bold4` - 20px

### Heading Styles
- `TypoSkin.notoSansJP.heading1` - 56px (Bold)
- `TypoSkin.notoSansJP.heading2` - 46px (Bold)
- `TypoSkin.notoSansJP.heading3` - 38px (SemiBold)
- `TypoSkin.notoSansJP.heading4` - 30px (SemiBold)
- `TypoSkin.notoSansJP.heading5` - 22px (SemiBold)
- `TypoSkin.notoSansJP.heading6` - 20px (Medium)

### Body Styles
- `TypoSkin.notoSansJP.body1` - 18px
- `TypoSkin.notoSansJP.body2` - 16px
- `TypoSkin.notoSansJP.body3` - 14px

### Button Styles
- `TypoSkin.notoSansJP.buttonText1` - 16px (Medium)
- `TypoSkin.notoSansJP.buttonText2` - 14px (Medium)

### Other Styles
- `TypoSkin.notoSansJP.subtitle1` - 18px
- `TypoSkin.notoSansJP.subtitle2` - 16px
- `TypoSkin.notoSansJP.caption` - 12px
- `TypoSkin.notoSansJP.light1` - 12px (Light)
- `TypoSkin.notoSansJP.light2` - 14px (Light)
- `TypoSkin.notoSansJP.light3` - 16px (Light)

## Font Installation

After adding or modifying fonts, you need to link them to your React Native project:

### For React Native 0.60+
```bash
npx react-native-asset
```

### For older versions
```bash
react-native link
```

### Manual Installation (if needed)

#### iOS
1. Add font files to your iOS project in Xcode
2. Update `Info.plist` with font names
3. Clean and rebuild the project

#### Android
1. Copy font files to `android/app/src/main/assets/fonts/`
2. Clean and rebuild the project

## Example Component

See `src/components/NotoSansJPExample.tsx` for a complete example of how to use all the available Noto Sans JP typography styles.

## Best Practices

1. **Consistency**: Use the predefined typography styles for consistency across your app
2. **Performance**: Prefer static font files over variable fonts for better performance on older devices
3. **Fallbacks**: Always provide fallback fonts in case Noto Sans JP fails to load
4. **Testing**: Test on both iOS and Android devices to ensure proper font rendering

## Troubleshooting

### Font not displaying
1. Ensure fonts are properly linked using `npx react-native-asset`
2. Check that font names match exactly (case-sensitive)
3. Clean and rebuild your project
4. Verify font files are in the correct directory

### Performance issues
1. Use static font files instead of variable fonts
2. Limit the number of font weights used in your app
3. Consider font subsetting for production builds

## License

Noto Sans JP is licensed under the SIL Open Font License (OFL). See `OFL.txt` for details.

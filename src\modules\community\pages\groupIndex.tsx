import {useNavigation, DrawerActions} from '@react-navigation/native';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
  View,
  TouchableWithoutFeedback,
  Keyboard,
  Text,
} from 'react-native';
// import {SafeAreaView} from 'react-native-safe-area-context';
import {
  AppButton,
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {FlatList, RefreshControl} from 'react-native-gesture-handler';
import SocialGroups from '../groups/listview/groups';
import {navigate, RootScreen} from '../../../router/router';
import FollowingGroup from '../groups/listview/followingGroup';
import CreatorGroup from '../groups/listview/creatorGroup';
import {useRef, useState} from 'react';
import {useDispatch} from 'react-redux';
import {GroupActions} from '../reducers/groupReducer';
import {followingGroupsActions} from '../reducers/followingGroupsReducer';
import {myGroupsActions} from '../reducers/myGroupsReducer';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {useForm} from 'react-hook-form';
import {randomGID} from '../../../utils/Utils';
import {TextFieldForm} from '../../Default/form/component-form';
import {TypoSkin} from '../../../assets/skin/typography';
import {BaseDA} from '../../../base/BaseDA';
import ImagePicker from 'react-native-image-crop-picker';
import {ProfileView} from './Chat';
import {SearchGroupIndex} from '../groups/listview/search';
import ConfirmGroup from '../groups/listview/confirmGroup';
import {useTranslation} from 'react-i18next';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {LogoImg} from '../../../Screen/Page/Home';

export default function Groups() {
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const [refreshing, setRefreshing] = useState(false);
  const dispatch = useDispatch<any>();
  const user = useSelectorCustomerState().data;
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {Id: randomGID(), DateCreated: new Date()},
  });
  const onRefresh = async () => {
    setRefreshing(true);
    dispatch(GroupActions.getAllGroups(1, 10));
    dispatch(followingGroupsActions.getFollowingGroups(1, 10));
    dispatch(myGroupsActions.getMyGroups(1, 10));

    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const errorForm = (error: any) => {
    console.log('error', error);
  };
  const submitForm = async (data: any) => {
    await dispatch(GroupActions.addGroup(data));
    methods.reset();
    hideBottomSheet(bottomSheetRef);
    navigation.push(RootScreen.GroupIndex, {Id: data.Id});
  };

  const renderHeader = () => (
    <ListTile
      style={{
        padding: 0,
        paddingBottom: 8,
        paddingHorizontal: 16,
      }}
      isClickLeading
      leading={
        <TouchableOpacity
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
          style={{padding: 4}}>
          <LogoImg />
        </TouchableOpacity>
      }
      title={t('community.tabs.group')}
      trailing={
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <TouchableOpacity
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: '#f0f0f0',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 8,
            }}
            onPress={() => {
              if (!user) {
                dialogCheckAcc(dialogRef);
                return;
              }
              showBottomSheet({
                ref: bottomSheetRef,
                enableDismiss: true,
                title: t('community.createGroup'),
                suffixAction: (
                  <AppButton
                    title={t('common.create')}
                    backgroundColor={ColorThemes.light.transparent}
                    textColor={ColorThemes.light.Primary_Color_Main}
                    borderColor="transparent"
                    containerStyle={{padding: 4}}
                    onPress={methods.handleSubmit(submitForm, errorForm)}
                  />
                ),
                prefixAction: (
                  <TouchableOpacity
                    onPress={() => hideBottomSheet(bottomSheetRef)}
                    style={{padding: 6, alignItems: 'center'}}>
                    <Winicon
                      src="outline/layout/xmark"
                      size={20}
                      color={ColorThemes.light.Neutral_Text_Color_Body}
                    />
                  </TouchableOpacity>
                ),

                children: <CreateGroup methods={methods} />,
              });
            }}>
            <Winicon
              src="outline/layout/plus"
              size={20}
              color={ColorThemes.light.Neutral_Text_Color_Title}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: '#f0f0f0',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 8,
            }}
            onPress={() => {
              showBottomSheet({
                ref: bottomSheetRef,
                enableDismiss: true,
                children: <SearchGroupIndex ref={bottomSheetRef} />,
              });
            }}>
            <Winicon
              src="outline/user interface/search"
              size={20}
              color={ColorThemes.light.Neutral_Text_Color_Title}
            />
          </TouchableOpacity>
          <ProfileView />
        </View>
      }
    />
  );

  const sections = [
    {
      key: 'Following',
      component: (
        <FollowingGroup
          id="Following"
          titleList={t('community.following')}
          horizontal
          isSeeMore
          onPressSeeMore={() => {
            navigate(RootScreen.AllGroupsLoadMore, {
              id: 'Following',
              title: 'theo dõi',
            });
          }}
        />
      ),
    },
    {
      key: 'Confirm',
      component: (
        <ConfirmGroup
          id="Confirm"
          titleList={t('community.confirm')}
          horizontal
          isSeeMore
          onPressSeeMore={() => {
            navigate(RootScreen.AllGroupsLoadMore, {
              id: 'Confirm',
              title: 'cần xác nhận',
            });
          }}
        />
      ),
    },
    {
      key: 'Moderration',
      component: (
        <CreatorGroup
          id="Moderration"
          titleList={t('community.moderation')}
          horizontal
          isSeeMore
          onPressSeeMore={() => {
            navigate(RootScreen.AllGroupsLoadMore, {
              id: 'Moderration',
              title: 'của bạn',
            });
          }}
        />
      ),
    },
    {
      key: 'Groups',
      component: (
        <SocialGroups
          titleList={t('community.groups')}
          isSeeMore={true}
          onPressSeeMore={() => {
            navigate(RootScreen.AllGroupsLoadMore, {title: 'khác'});
          }}
        />
      ),
    },
  ];

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FDialog ref={dialogRef} />
      <FBottomSheet ref={bottomSheetRef} />
      {renderHeader()}
      <FlatList
        data={user ? sections : sections.filter(a => a.key == 'Groups')}
        style={{flex: 1, paddingTop: 24}}
        renderItem={({item}) => item.component}
        keyExtractor={(_, index) => index.toString()}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      />
    </View>
  );
}

const CreateGroup = ({methods}: any) => {
  const {t} = useTranslation();
  const [img, setImg] = useState<any>(null);
  const pickerImg = async () => {
    const img = await ImagePicker.openPicker({
      multiple: false,
      cropping: false,
    });
    if (img) {
      const resImgs = await BaseDA.uploadFiles([
        {
          uri: img.path,
          type: img.mime,
          name: img.filename ?? 'new file img',
        },
      ]);
      if (resImgs) {
        setImg(resImgs[0].Id);
        methods.setValue('Thumb', resImgs[0].Id);
      }
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View
        style={{
          height: Dimensions.get('window').height * 0.9, // Tăng chiều cao lên gần như toàn màn hình
          width: '100%',
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}>
        <KeyboardAvoidingView
          style={{flex: 1, width: '100%'}}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}>
          <ScrollView
            style={{flex: 1}}
            contentContainerStyle={{
              padding: 16,
            }}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            keyboardDismissMode="on-drag">
            <View style={{gap: 24, width: '100%'}}>
              <TextFieldForm
                required
                style={{
                  width: '100%',
                  backgroundColor: '#fff',
                  borderRadius: 8,
                }}
                placeholder={t('community.groupName')}
                label={t('community.groupName')}
                control={methods.control}
                errors={methods.formState.errors}
                register={methods.register}
                name="Name"
                textFieldStyle={{padding: 16}}
              />
              <Text style={{...TypoSkin.buttonText4}}>
                {t('community.groupImage')}
              </Text>
              {img ? (
                <View
                  style={{
                    width: Dimensions.get('window').width - 32,
                    height: 100,
                    borderWidth: 1.3,
                    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
                    borderStyle: 'dashed',
                    borderRadius: 8,
                    padding: 8,
                  }}>
                  <TouchableOpacity
                    onPress={async () => {
                      setImg(null);
                    }}
                    style={{
                      padding: 4,
                      position: 'absolute',
                      top: 8,
                      right: 8,
                    }}>
                    <Winicon
                      src="fill/user interface/c-remove"
                      size={20}
                      color={ColorThemes.light.Error_Color_Main}
                    />
                  </TouchableOpacity>
                  <SkeletonImage
                    source={{uri: ConfigAPI.urlImg + img}}
                    style={{
                      width: '100%',
                      height: '100%',

                      objectFit: 'none',
                    }}
                  />
                </View>
              ) : (
                <TouchableOpacity
                  onPress={pickerImg}
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 8,
                    borderWidth: 1.3,
                    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
                    borderStyle: 'dashed',
                    borderRadius: 8,
                    padding: 8,
                  }}>
                  <SkeletonImage
                    source={{
                      uri: 'https://file-mamager.wini.vn/Upload/2024/12/card3_7f8d.png',
                    }}
                    style={{
                      width: 35,
                      height: 35,
                      objectFit: 'cover',
                    }}
                  />
                  <Text
                    numberOfLines={1}
                    style={{
                      ...TypoSkin.buttonText4,
                      color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    }}>
                    {t('community.addPhoto')}
                  </Text>
                </TouchableOpacity>
              )}
              <TextFieldForm
                label={t('common.description')}
                textStyle={{textAlignVertical: 'top'}}
                numberOfLines={10}
                multiline={true}
                textFieldStyle={{
                  paddingHorizontal: 16,
                  paddingTop: 16,
                  paddingBottom: 16,
                  height: 100,
                  justifyContent: 'flex-start',
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                }}
                style={{width: '100%'}}
                register={methods.register}
                control={methods.control}
                errors={methods.formState.errors}
                name="Description"
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </TouchableWithoutFeedback>
  );
};

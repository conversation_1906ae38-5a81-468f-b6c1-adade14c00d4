import axios from 'axios';
import { NativeEventEmitter, NativeModules, Alert } from 'react-native';
import VnpayMerchant, { VnpayMerchantModule } from '../../react-native-vnpay-merchant';

// Add null check to prevent NativeEventEmitter error
const eventEmitter = VnpayMerchantModule ? new NativeEventEmitter(VnpayMerchantModule) : null;

// Hàm tạo payment URL từ backend

// Hàm mở VNPAY native SDK
export const openVnpay = (paymentUrl) => {
  if (!VnpayMerchant || !VnpayMerchant.show) {
    Alert.alert('Lỗi', 'VNPAY SDK chưa được cài đặt đúng cách');
    return;
  }

  VnpayMerchant.show({
    paymentUrl,
    scheme: 'vnpayITM',  // phải khớp với scheme app đã đăng ký deeplink
    isSandbox: true,
    tmn_code: 'ITMETEST', // Thêm tmn_code là bắt buộc
    title: '<PERSON>h toán',
    backAlert: 'Bạn có chắc chắn muốn hủy thanh toán?',
    beginColor: '#F06744',
    endColor: '#E26F2C',
    titleColor: '#FFFFFF'
  });
};

// Hàm lắng nghe kết quả thanh toán
export const listenVnpayCallback = (onResult) => {
  if (!eventEmitter) {
    console.warn('VNPAY EventEmitter not available');
    return () => { }; // Return empty cleanup function
  }

  const subscription = eventEmitter.addListener('PaymentBack', (result) => {
    console.log('VNPAY Payment result:', result);
    onResult(result);
  });

  return () => subscription.remove();  // clear listener khi không dùng nữa
};

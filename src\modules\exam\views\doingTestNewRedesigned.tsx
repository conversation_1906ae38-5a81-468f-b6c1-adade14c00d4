/* eslint-disable react-native/no-inline-styles */
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  Image,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import {navigateBack, RootScreen} from '../../../router/router';
import {
  AppButton,
  Checkbox,
  FBottomSheet,
  hideBottomSheet,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';
import {useCallback, useEffect, useMemo, useRef, useState, memo} from 'react';
import {RadioButton} from 'react-native-paper';
import CountdownTimer from '../components/countDownTimer';
import AnimatedTimerIcon from '../components/AnimatedTimerIcon';
import ClickableImage from '../components/ClickableImage';
import PDFViewer from '../../Course/components/PDFViewer';
import {useDispatch} from 'react-redux';
import store, {AppDispatch} from '../../../redux/store/store';
import {useNavigation, useRoute} from '@react-navigation/native';
import {ExamActions} from '../../../redux/reducers/examReducer';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';
import RenderHTML from 'react-native-render-html';
import {DataController} from '../../../base/baseController';
import {Sakupi, SakupiType, StatusExam} from '../../../Config/Contanst';
import {randomGID} from '../../../utils/Utils';
import ConfigAPI from '../../../Config/ConfigAPI';
import Sound from 'react-native-sound';
import FastImage from 'react-native-fast-image';
import {BaseDA} from '../../../base/BaseDA';
import {LessonActions} from '../../../redux/reducers/proccessLessonReducer';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';

// Global helper function to safely render HTML content
export const getSafeHtmlContent = (
  content: any,
  fallback: string = '<p>No content</p>',
) => {
  try {
    if (!content) {
      console.log('⚠️ getSafeHtmlContent: content is null/undefined');
      return fallback;
    }
    if (typeof content !== 'string') {
      console.log(
        '⚠️ getSafeHtmlContent: content is not string:',
        typeof content,
        content,
      );
      return fallback;
    }
    if (content.trim() === '') {
      console.log('⚠️ getSafeHtmlContent: content is empty string');
      return fallback;
    }
    console.log('✅ getSafeHtmlContent: valid content');
    return content;
  } catch (error) {
    console.error('❌ getSafeHtmlContent error:', error);
    return fallback;
  }
};

// Memoized Answer Component for better performance
const AnswerItem = memo(
  ({
    answer,
    questionId,
    selectionType,
    onSelect,
    index,
  }: {
    answer: any;
    questionId: string;
    selectionType: number;
    onSelect: (questionId: string, answerId: string) => void;
    index: number;
  }) => {
    const handlePress = useCallback(() => {
      onSelect(questionId, answer.Id);
    }, [onSelect, questionId, answer.Id]);

    return (
      <TouchableOpacity
        onPress={handlePress}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 12,
          paddingVertical: 4,
          borderRadius: 8,
          borderWidth: 1,
          borderColor: answer.choose
            ? ColorThemes.light.Info_Color_Main
            : ColorThemes.light.Neutral_Border_Color_Main,
          backgroundColor: answer.choose
            ? ColorThemes.light.primary_background
            : ColorThemes.light.Neutral_Background_Color_Absoluteary_Color_Main,
        }}>
        {/* Answer Selection Icon */}
        <View style={{marginRight: 12}} pointerEvents="none">
          {selectionType === 2 ? (
            <Checkbox
              value={answer.choose ?? false}
              onChange={() => {}} // Disabled - handled by TouchableOpacity
              checkboxStyle={{
                backgroundColor: ColorThemes.light.Info_Color_Main,
              }}
            />
          ) : (
            <RadioButton.Android
              value={answer.Id}
              status={answer.choose ? 'checked' : 'unchecked'}
              color={ColorThemes.light.Info_Color_Main}
              onPress={() => {}} // Disabled - handled by TouchableOpacity
            />
          )}
        </View>

        {/* Answer Content */}
        <View style={{flex: 1}}>
          {/* Answer Text */}
          {(answer.Content || answer.Name) && (
            <Text
              style={{
                ...TypoSkin.body2,
                color: answer.choose
                  ? ColorThemes.light.Info_Color_Main
                  : ColorThemes.light.neutral_text_body_color,
                marginBottom: answer.Img ? 8 : 0,
              }}>
              {`${index + 1}: ${answer.Content || answer.Name}`}
            </Text>
          )}

          {/* Answer Image */}
          {answer.Img && (
            <View style={{marginTop: 4}}>
              <ClickableImage
                source={{
                  uri: answer.Img?.includes('http')
                    ? answer.Img
                    : ConfigAPI.urlImg + answer.Img,
                }}
                style={{
                  width: '100%',
                  height: 60,
                  borderRadius: 6,
                }}
                resizeMode="contain"
              />
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  },
);

// Memoized Question Component
const QuestionItem = memo(
  ({
    question,
    questionIndex,
    onAnswerSelect,
    onPlayAudio,
    currentPlayingAudio,
    isPaused,
    t,
  }: {
    question: any;
    questionIndex: number;
    onAnswerSelect: (questionId: string, answerId: string) => void;
    onPlayAudio: (audioUrl: string) => void;
    currentPlayingAudio: string | null;
    isPaused: boolean;
    t: any;
  }) => {
    return (
      <View style={{marginBottom: 24, paddingHorizontal: 16}}>
        {/* Question Header */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 4,
            paddingVertical: 8,
          }}>
          <Text
            style={{
              ...TypoSkin.subtitle2,
              fontWeight: '700',
              color: ColorThemes.light.Neutral_Text_Color_Title,
              marginRight: 8,
            }}>
            {`${questionIndex + 1}:`}
          </Text>
          <Text
            style={{
              ...TypoSkin.body3,
              color: ColorThemes.light.neutral_text_subtitle_color,
            }}>
            ({' '}
            {question.SelectionType === 2
              ? t('exam.selectMultipleAnswers')
              : t('exam.selectOneAnswer')}{' '}
            )
          </Text>
        </View>

        {/* Question Content */}
        <View style={{marginBottom: 16}}>
          <RenderHTML
            contentWidth={Dimensions.get('window').width - 32}
            source={{
              html: getSafeHtmlContent(
                question.Title || question.Name || question.Content,
                '<p>No content available</p>',
              ),
            }}
            tagsStyles={{
              body: {
                fontSize: 16,
                lineHeight: 24,
                fontFamily: 'Inter',
              },
              p: {
                fontSize: 16,
                lineHeight: 24,
                fontFamily: 'Inter',
              },
              img: {
                marginVertical: 10,
                alignSelf: 'center',
                borderRadius: 8,
                borderWidth: 1,
                maxWidth: '100%',
                height: 200,
              },
            }}
            renderers={{
              img: ({TDefaultRenderer, ...props}: any) => {
                const {src} = props.tnode.attributes;
                if (src) {
                  return (
                    <ClickableImage
                      source={{uri: src}}
                      style={{
                        marginVertical: 10,
                        alignSelf: 'center',
                        borderRadius: 8,
                        borderWidth: 1,
                        maxWidth: '100%',
                        height: 200,
                      }}
                      resizeMode="contain"
                    />
                  );
                }
                return <TDefaultRenderer {...props} />;
              },
            }}
          />
        </View>

        {/* Question Image (separate field) */}
        {question.Img && (
          <View
            style={{
              marginBottom: 12,
              borderRadius: 8,
              overflow: 'hidden',
              backgroundColor: ColorThemes.light.neutral_main_background_color,
            }}>
            <ClickableImage
              source={{
                uri: question.Img?.includes('http')
                  ? question.Img
                  : ConfigAPI.urlImg + question.Img,
              }}
              style={{
                width: '100%',
                height: 200,
                borderRadius: 8,
              }}
              resizeMode="contain"
            />
          </View>
        )}

        {/* Question Audio (separate field) */}
        {question.Audio && (
          <View
            style={{
              marginBottom: 12,
              paddingHorizontal: 12,
              paddingVertical: 8,
              backgroundColor: ColorThemes.light.neutral_main_background_color,
              borderRadius: 8,
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <TouchableOpacity
              onPress={() => {
                if (question.Audio) {
                  const audioUrl = question.Audio?.includes('http')
                    ? question.Audio
                    : `${ConfigAPI.url.replace('/api/', '')}${question.Audio}`;
                  onPlayAudio(audioUrl);
                }
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                flex: 1,
              }}>
              <Winicon
                src={
                  currentPlayingAudio === (question.Audio?.includes('http')
                    ? question.Audio
                    : `${ConfigAPI.url.replace('/api/', '')}${question.Audio}`) && !isPaused
                    ? 'color/multimedia/button-pause'
                    : currentPlayingAudio === (question.Audio?.includes('http')
                      ? question.Audio
                      : `${ConfigAPI.url.replace('/api/', '')}${question.Audio}`) && isPaused
                    ? 'color/multimedia/button-play'
                    : 'fill/multimedia/sound'
                }
                size={24}
                color={ColorThemes.light.Info_Color_Main}
                style={{marginRight: 8}}
              />
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.Info_Color_Main,
                  flex: 1,
                }}>
                {currentPlayingAudio === (question.Audio?.includes('http')
                  ? question.Audio
                  : `${ConfigAPI.url.replace('/api/', '')}${question.Audio}`) && !isPaused
                  ? 'Đang nghe (Bấm để tạm dừng)'
                  : currentPlayingAudio === (question.Audio?.includes('http')
                    ? question.Audio
                    : `${ConfigAPI.url.replace('/api/', '')}${question.Audio}`) && isPaused
                  ? 'Đã tạm dừng (Bấm để tiếp tục)'
                  : t('exam.listenToQuestion')}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Answers */}
        <View style={{gap: 8}}>
          {question.lstAnswer?.map((answer: any, index: number) => (
            <AnswerItem
              key={answer.Id}
              answer={answer}
              index={index}
              questionId={question.Id}
              selectionType={question.SelectionType ?? 1}
              onSelect={onAnswerSelect}
            />
          ))}
        </View>
      </View>
    );
  },
);

export default function DoingTestNewRedesigned() {
  const {t} = useTranslation();
  const route = useRoute<any>();
  const {testId, Step, type} = route.params;
  const dispatch: AppDispatch = useDispatch();

  const scrollSectionRef = useRef<any>(null);
  const questionsListRef = useRef<any>(null); // Ref for questions FlatList

  // State management
  const [testData, setTestData] = useState<any>();
  const [sections, setSections] = useState<any[]>([]);
  const [exams, setExams] = useState<any[]>([]);
  const [allQuestions, setAllQuestions] = useState<any[]>([]); // Cache all questions
  const [currentQuestions, setCurrentQuestions] = useState<any[]>([]);
  const [selectedSection, setSelectedSection] = useState<any>(null);
  const [selectedExam, setSelectedExam] = useState<any>(null);
  const [currentExamIndex, setCurrentExamIndex] = useState(0);
  const [loading, setLoading] = useState(true);

  // New states for section-based exam logic
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [submittedSections, setSubmittedSections] = useState<Set<string>>(
    new Set(),
  );
  const [currentSectionTime, setCurrentSectionTime] = useState<number>(0);
  const [sectionTimerKey, setSectionTimerKey] = useState<number>(0); // Force timer reset
  const [pendingNextSection, setPendingNextSection] = useState<any>(null); // Track pending section change
  //navigate
  const navigation = useNavigation<any>();

  // Controllers
  const examController = useMemo(() => new DataController('Exam'), []);
  const testResultController = useMemo(
    () => new DataController('Test_Result'),
    [],
  );
  const answerController = useMemo(() => new DataController('Answer'), []);

  // Use ref to store user answers to prevent race conditions
  const userAnswersRef = useRef<{
    [questionId: string]: {[answerId: string]: boolean};
  }>({});

  const bottomSheetRef = useRef<any>(null);

  // State for exam media collapse
  const [showExamImage, setShowExamImage] = useState(false);
  const [currentPlayingAudio, setCurrentPlayingAudio] = useState<string | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);

  // State for timer animation
  const [isHalfTimeReached, setIsHalfTimeReached] = useState(false);

  // Callback for when half time is reached
  const handleHalfTimeReached = useCallback(() => {
    console.log('🕐 Half time reached - starting timer animation');
    setIsHalfTimeReached(true);
  }, []);

  // Helper function to apply user answers to questions
  const applyUserAnswersToQuestions = useCallback((questions: any[]) => {
    return questions.map(question => {
      if (!question.lstAnswer) {
        return question;
      }

      return {
        ...question,
        lstAnswer: question.lstAnswer.map((answer: any) => ({
          ...answer,
          choose: userAnswersRef.current[question.Id]?.[answer.Id] || false,
        })),
      };
    });
  }, []);

  // Helper function to get questions with preserved answers
  const getQuestionsWithAnswers = useCallback(
    (questionIds: string[]) => {
      const questions = questionIds
        .map((questionId: any) => {
          return allQuestions.find((q: any) => q.Id === questionId);
        })
        .filter(Boolean);

      return applyUserAnswersToQuestions(questions);
    },
    [allQuestions, applyUserAnswersToQuestions],
  );

  // Load initial test data
  useEffect(() => {
    const getTestData = async () => {
      if (testId) {
        try {
          const res = await examController.getPatternList({
            query: `@TestId:{${testId}}`,
            pattern: {
              TestId: ['Id', 'Name', 'Time', 'Level', 'SectionId', 'Score'],
              SectionId: ['Id', 'Name', 'Time', 'Score'],
              QuestionId: [
                'Id',
                'Name',
                'Title',
                'SelectionType',
                'Score',
                'Audio',
                'Img',
                'Pdf',
              ],
            },
          });
          if (res.code === 200) {
            setTestData(res.Test[0]);
            const sectionsData = res.Test[0]?.SectionId?.split(',').map(
              (id: any) => res.Section.find((e: any) => e.Id === id),
            );
            //loại bỏ những item undefined

            const filteredSections = sectionsData.filter(Boolean);
            // get all questions in a section and remove duplicate question ids
            const questionsInSections = filteredSections.map((section: any) => {
              const sectionExams = res.data.filter(
                (exam: any) => exam.SectionId === section.Id,
              );
              var questionIds = sectionExams
                .map((exam: any) => exam.QuestionId?.split(','))
                .flat();

              // kiểm tra xem có Id trùng thì loại bỏ
              // questionIds = questionIds.filter((id: any, index: number) => {
              //   return questionIds.indexOf(id) === index;
              // });
              const uniqueQuestionIds = [...questionIds];
              return uniqueQuestionIds;
            });

            // map questions total to filteredSections
            const sectionsWithQuestions = filteredSections.map(
              (section: any, index: number) => ({
                ...section,
                totalQuestions: questionsInSections[index].length,
              }),
            );

            setSections(sectionsWithQuestions);

            const examsData = res.data.sort(
              (a: any, b: any) => a.Sort - b.Sort,
            );
            examsData.map(async (exam: any) => {
              const rs = await BaseDA.getFilesInfor([exam.Audio]);
              if (rs && rs.data && rs.data.length > 0) {
                exam.Audio = rs.data[0].Url;
              }
            });
            setExams(examsData);
            // Store questions temporarily

            // check selectionType == null => 1 chon 1
            if (res.Question) {
              res.Question = res.Question?.map((item: any) => {
                return {...item, SelectionType: item?.SelectionType || 1};
              });
            }

            setAllQuestions(res.Question);

            // Auto select first section and exam
            if (sectionsData.length > 0) {
              setSelectedSection(sectionsData[0]);
              setCurrentSectionIndex(0);
              // Set initial section time
              setCurrentSectionTime(sectionsData[0]?.Time || 0);
              setSectionTimerKey(Date.now()); // Force timer reset

              const firstSectionExams = examsData.filter(
                (exam: any) => exam.SectionId === sectionsData[0].Id,
              );
              if (firstSectionExams.length > 0) {
                setSelectedExam(firstSectionExams[0]);
                // Set initial questions (answers will be combined later)
                const initialQuestions =
                  firstSectionExams[0]?.QuestionId?.split(',')
                    .map((questionId: any) => {
                      return res.Question.find((q: any) => q.Id === questionId);
                    })
                    .filter(Boolean);
                setCurrentQuestions(initialQuestions);
              }
            }
          }
        } catch (error) {
          console.error('Error loading test data:', error);
        } finally {
          setLoading(false);
        }
      }
    };
    getTestData();
  }, [testId, examController]);

  // Load answers and combine with questions
  useEffect(() => {
    if (allQuestions.length > 0) {
      console.log('🔄 Loading answers for questions...', allQuestions.length);

      answerController
        .getListSimple({
          query: `@QuestionId:{${allQuestions.map(e => e.Id).join(' | ')}}`,
          returns: ['Id', 'Name', 'Content', 'Sort', 'QuestionId', 'IsResult','Img','Audio'],
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        })
        .then(res => {
          if (res.code === 200) {
            console.log(
              '✅ Answers loaded, combining with questions...',
              res.data.length,
            );
            // Combine questions with answers and update allQuestions
            const questionsWithAnswers = allQuestions.map((question: any) => ({
              ...question,
              lstAnswer: res.data
                .filter((answer: any) => answer.QuestionId === question.Id)
                .map((answer: any) => ({
                  ...answer,
                  choose: false, // Initialize as not chosen
                })),
            }));

            console.log(
              '📋 Questions with answers:',
              questionsWithAnswers[0]?.lstAnswer?.length,
            );
            setAllQuestions(questionsWithAnswers);

            // Update currentQuestions if they exist
            if (currentQuestions.length > 0) {
              const updatedCurrentQuestions = currentQuestions.map(
                (question: any) => {
                  const questionWithAnswers = questionsWithAnswers.find(
                    (q: any) => q.Id === question.Id,
                  );
                  if (questionWithAnswers) {
                    // Apply user answers from ref
                    return {
                      ...questionWithAnswers,
                      lstAnswer: questionWithAnswers.lstAnswer.map(
                        (answer: any) => ({
                          ...answer,
                          choose:
                            userAnswersRef.current[question.Id]?.[answer.Id] ||
                            false,
                        }),
                      ),
                    };
                  }
                  return question;
                },
              );
              console.log('🔄 Updated currentQuestions with answers');
              setCurrentQuestions(updatedCurrentQuestions);
            }
          } else {
            console.log('❌ Failed to load answers:', res);
          }
        })
        .catch(error => {
          console.log('❌ Error loading answers:', error);
        });
    }
  }, [allQuestions.length, answerController, currentQuestions.length]);
  // Handle section selection - Only allow sequential access
  const handleSectionChange = (section: any) => {
    const sectionIndex = sections.findIndex(s => s.Id === section.Id);
    const currentIndex = sections.findIndex(s => s.Id === selectedSection?.Id);

    console.log('🔄 handleSectionChange called:');
    console.log('  - Target section:', section.Name, 'index:', sectionIndex);
    console.log(
      '  - Current section:',
      selectedSection?.Name,
      'index:',
      currentIndex,
    );
    console.log('  - Submitted sections:', Array.from(submittedSections));
    console.log(
      '  - Current section submitted?',
      submittedSections.has(sections[currentIndex]?.Id),
    );

    // Only allow access to current section or next section if current is submitted
    const canAccessNext =
      sectionIndex === currentIndex + 1 &&
      submittedSections.has(sections[currentIndex]?.Id);
    const canAccessCurrent = sectionIndex === currentIndex;

    if (!canAccessCurrent && !canAccessNext) {
      console.log(
        '❌ Can only access current section or next section after submitting current one',
      );
      console.log('  - canAccessCurrent:', canAccessCurrent);
      console.log('  - canAccessNext:', canAccessNext);
      return;
    }

    // Cannot go back to submitted sections
    if (submittedSections.has(section.Id)) {
      console.log('❌ Cannot return to submitted section');
      return;
    }

    console.log(`🔄 Changing to section: ${section.Name}`);
    setSelectedSection(section);
    setCurrentSectionIndex(sectionIndex);

    // Set section time and reset timer
    setCurrentSectionTime(section?.Time || 0);
    setSectionTimerKey(Date.now());
    setIsHalfTimeReached(false);

    // scrollSectionRef scroll to center with index
    scrollSectionRef.current?.scrollTo({
      x: (sectionIndex - 1) * 100,
      animated: true,
    });

    const sectionExams = exams.filter(
      (exam: any) => exam.SectionId === section.Id,
    );
    if (sectionExams.length > 0) {
      setSelectedExam(sectionExams[0]);
      setCurrentExamIndex(0);
      const questionIds = sectionExams[0]?.QuestionId?.split(',');
      const examQuestions = getQuestionsWithAnswers(questionIds);
      console.log(
        `📋 Setting ${examQuestions.length} questions for exam ${sectionExams[0].Name}`,
      );
      setCurrentQuestions(examQuestions);
    }
  };

  // Handle exam navigation - No loading, instant switch
  const handleNextExam = () => {
    const sectionExams = exams.filter(
      (exam: any) => exam.SectionId === selectedSection.Id,
    );
    if (currentExamIndex < sectionExams.length - 1) {
      const nextExam = sectionExams[currentExamIndex + 1];
      setSelectedExam(nextExam);
      setCurrentExamIndex(currentExamIndex + 1);
      // Reset timer animation state when changing exams
      setIsHalfTimeReached(false);
      const questionIds = nextExam?.QuestionId?.split(',');
      const examQuestions = getQuestionsWithAnswers(questionIds);
      setCurrentQuestions(examQuestions);
    }
  };

  const handlePreviousExam = () => {
    const sectionExams = exams.filter(
      (exam: any) => exam.SectionId === selectedSection.Id,
    );
    if (currentExamIndex > 0) {
      const prevExam = sectionExams[currentExamIndex - 1];
      setSelectedExam(prevExam);
      setCurrentExamIndex(currentExamIndex - 1);
      // Reset timer animation state when changing exams
      setIsHalfTimeReached(false);
      const questionIds = prevExam?.QuestionId?.split(',');
      const examQuestions = getQuestionsWithAnswers(questionIds);
      setCurrentQuestions(examQuestions);
    }
  };

  // Handle answer selection - Preserve answers across sections/exams
  const handleAnswerSelect = useCallback(
    (questionId: string, answerId: string) => {
      console.log(`🎯 Selecting answer ${answerId} for question ${questionId}`);
      dispatch(ExamActions.choose(questionId, answerId));

      // Find the question to get SelectionType
      const question = currentQuestions.find(q => q.Id === questionId);
      if (!question) {
        console.log(`❌ Question ${questionId} not found`);
        return;
      }

      // Update userAnswersRef first
      if (!userAnswersRef.current[questionId]) {
        userAnswersRef.current[questionId] = {};
      }

      if (question.SelectionType === 1) {
        // Single choice - clear all answers for this question first
        userAnswersRef.current[questionId] = {};
        userAnswersRef.current[questionId][answerId] = true;
      } else {
        // Multiple choice - toggle this answer
        const currentValue =
          userAnswersRef.current[questionId][answerId] || false;
        userAnswersRef.current[questionId][answerId] = !currentValue;
      }

      console.log(
        '💾 Saved to userAnswersRef:',
        userAnswersRef.current[questionId],
      );

      // Update currentQuestions with new answers
      setCurrentQuestions(prevQuestions => {
        return prevQuestions.map(q => {
          if (q.Id === questionId) {
            return {
              ...q,
              lstAnswer: q.lstAnswer.map((answer: any) => ({
                ...answer,
                choose:
                  userAnswersRef.current[questionId]?.[answer.Id] || false,
              })),
            };
          }
          return q;
        });
      });

      console.log(`✅ Updated currentQuestions for question ${questionId}`);
    },
    [dispatch, currentQuestions],
  );

  // Function to handle section submission
  const handleSectionSubmit = useCallback(() => {
    if (!selectedSection) return;

    // Show section overview modal
    showBottomSheet({
      ref: bottomSheetRef,
      enableDismiss: true,
      title: `${t('exam.submitSection')}: ${selectedSection.Name}`,
      prefixAction: <View />,
      suffixAction: (
        <TouchableOpacity
          onPress={() => hideBottomSheet(bottomSheetRef)}
          style={{padding: 6, alignItems: 'center'}}>
          <Winicon
            src="outline/layout/xmark"
            size={20}
            color={ColorThemes.light.Neutral_Text_Color_Body}
          />
        </TouchableOpacity>
      ),
      children: (
        <SectionOverview ref={bottomSheetRef} section={selectedSection} />
      ),
    });
  }, [selectedSection, t]);

  // Function to confirm section submission
  const confirmSectionSubmit = useCallback(() => {
    if (!selectedSection) return;

    console.log('🔄 Confirming section submit for:', selectedSection.Name);

    // Check if this is the last section
    const currentIndex = sections.findIndex(s => s.Id === selectedSection.Id);
    const isLastSection = currentIndex === sections.length - 1;

    console.log(
      '📍 Current section index:',
      currentIndex,
      'Is last section:',
      isLastSection,
    );

    // Mark section as submitted
    setSubmittedSections(prev => {
      const newSet = new Set([...prev, selectedSection.Id]);
      console.log('✅ Updated submitted sections:', Array.from(newSet));
      return newSet;
    });

    if (isLastSection) {
      // Last section - calculate final score and navigate to results
      console.log('🏁 Last section - calculating final results');
      const examResult = createDetailedResultData();
      submitFinalExam(examResult);
    } else {
      // Not last section - set pending next section
      const nextSection = sections[currentIndex + 1];
      console.log('➡️ Setting pending next section:', nextSection?.Name);
      if (nextSection) {
        setPendingNextSection(nextSection);
      }
    }
  }, [selectedSection, sections]);

  // Effect to handle section change after submission
  useEffect(() => {
    if (pendingNextSection) {
      console.log(
        '🔄 Processing pending section change to:',
        pendingNextSection.Name,
      );
      handleSectionChange(pendingNextSection);
      setPendingNextSection(null);
    }
  }, [submittedSections, pendingNextSection]);

  // Function to jump to specific question
  const jumpToQuestion = useCallback(
    (targetQuestionId: string, targetExam: any) => {
      console.log(
        '🎯 Jumping to question:',
        targetQuestionId,
        'in exam:',
        targetExam.Name,
      );

      // First, switch to the target exam if it's different from current exam
      const sectionExams = exams.filter(
        (exam: any) => exam.SectionId === selectedSection?.Id,
      );
      const targetExamIndex = sectionExams.findIndex(
        (exam: any) => exam.Id === targetExam.Id,
      );

      if (targetExamIndex !== currentExamIndex) {
        console.log('🔄 Switching to exam index:', targetExamIndex);
        setCurrentExamIndex(targetExamIndex);

        // Load questions for target exam with answers
        const questionIds = targetExam.QuestionId?.split(',') || [];
        const examQuestions = getQuestionsWithAnswers(questionIds);
        setCurrentQuestions(examQuestions);

        // Wait for questions to load, then scroll
        setTimeout(() => {
          const questionIndex = examQuestions.findIndex(
            (q: any) => q.Id === targetQuestionId,
          );
          if (questionIndex !== -1 && questionsListRef.current) {
            console.log('📍 Scrolling to question index:', questionIndex);
            questionsListRef.current.scrollToIndex({
              index: questionIndex,
              animated: true,
              viewPosition: 0.1, // Show question at top 10% of screen
            });
          }
        }, 300);
      } else {
        // Same exam, just scroll to question
        const questionIndex = currentQuestions.findIndex(
          (q: any) => q.Id === targetQuestionId,
        );
        if (questionIndex !== -1 && questionsListRef.current) {
          console.log('📍 Scrolling to question index:', questionIndex);
          questionsListRef.current.scrollToIndex({
            index: questionIndex,
            animated: true,
            viewPosition: 0.1, // Show question at top 10% of screen
          });
        }
      }

      // Close modal
      hideBottomSheet(bottomSheetRef);
    },
    [
      selectedSection,
      exams,
      currentExamIndex,
      currentQuestions,
      allQuestions,
      getQuestionsWithAnswers,
    ],
  );

  // Function to submit final exam results
  const submitFinalExam = useCallback(
    async (examResult: any) => {
      try {
        console.log('📊 Final Exam Result:', examResult);
        const customer = store.getState().customer.data;
        const testResultData = {
          Id: randomGID(),
          TestId: testId,
          CustomerId: customer?.Id,
          QuestionId: examResult.questionIds,
          AnswerId: examResult.answerIds,
          Score: examResult.totalScore,
          DateCreated: new Date().getTime(),
          Name: customer?.Name + ' ' + testData.Name,
          Status: examResult.isPassed ? StatusExam.passed : StatusExam.fail,
        };

        if (examResult.isPassed && type === 'Test' && Step) {
          dispatch(
            LessonActions.updateProcess({
              Id: Step?.lessonId,
              CourseId: Step?.courseId,
              lessonId: Step?.lessonId,
              stepOrder: Step?.StepOrder,
              PartId: Step?.PartId,
              PercentCompleted: 100,
              Type: 'Test',
            }),
          );
          dispatch(
            CustomerActions.updateRank(SakupiType.tryTest, Sakupi.tryTest),
          );
        }

        const result = await testResultController.add([testResultData]);
        if (result.code === 200) {
          navigation.replace(RootScreen.resultTestNew, {
            item: examResult,
          });
        }
      } catch (error) {
        console.error('❌ Error submitting final exam:', error);
        Alert.alert('Error', 'Error submitting exam. Please try again.');
      }
    },
    [testId, testData, type, Step, dispatch, testResultController, navigation],
  );

  // Function to collect all answers and calculate score with pass/fail logic
  const collectAnswersForSubmit = useCallback(() => {
    const answeredQuestions: string[] = [];
    const selectedAnswers: string[] = [];
    let totalScore = 0;
    let correctAnswers = 0;

    // Calculate score by section for elimination check
    const sectionScores: {
      [sectionId: string]: {
        score: number;
        maxScore: number;
        eliminationScore: number;
        sectionName: string;
      };
    } = {};

    // Initialize section scores
    sections.forEach((section: any) => {
      sectionScores[section.Id] = {
        score: 0,
        maxScore: 0,
        eliminationScore: section.Score || 0, // Điểm liệt của section
        sectionName: section.Name,
      };
    });

    // Loop through all questions to collect answers and calculate scores
    allQuestions.forEach((question: any) => {
      // Find which section this question belongs to
      const questionExam = exams?.find(
        (exam: any) =>
          exam.QuestionId && exam?.QuestionId?.split(',').includes(question.Id),
      );
      const sectionId = questionExam?.SectionId;

      if (sectionId && sectionScores[sectionId]) {
        // Add to max score for this section
        sectionScores[sectionId].maxScore += question.Score || 1;
      }

      const userAnswers = userAnswersRef.current[question.Id];

      if (userAnswers && Object.keys(userAnswers).length > 0) {
        // Get selected answer IDs for this question
        const selectedAnswerIds = Object.keys(userAnswers).filter(
          answerId => userAnswers[answerId] === true,
        );

        if (selectedAnswerIds.length > 0) {
          answeredQuestions.push(question.Id);
          selectedAnswers.push(...selectedAnswerIds);

          // Calculate score for this question
          const correctAnswerIds =
            question.lstAnswer
              ?.filter(
                (answer: any) =>
                  answer.IsResult === true ||
                  answer.IsResult === 1 ||
                  answer.IsResult === '1',
              )
              ?.map((answer: any) => answer.Id) || [];

          // Check if user's answers match correct answers
          const isCorrect =
            question.SelectionType === 1
              ? selectedAnswerIds.length === 1 &&
                correctAnswerIds.includes(selectedAnswerIds[0])
              : selectedAnswerIds.length === correctAnswerIds.length &&
                selectedAnswerIds.every((id: string) =>
                  correctAnswerIds.includes(id),
                ) &&
                correctAnswerIds.every((id: string) =>
                  selectedAnswerIds.includes(id),
                );
          if (isCorrect) {
            const questionScore = question.Score || 1;
            totalScore += questionScore;
            correctAnswers++;

            // Add score to section
            if (sectionId && sectionScores[sectionId]) {
              sectionScores[sectionId].score += questionScore;
            }
          }
        }
      }
    });

    // Check pass/fail logic
    let isPassed = true;
    let failReason = '';
    const sectionResults: any[] = [];

    // 1. Check elimination scores for each section
    Object.keys(sectionScores).forEach(sectionId => {
      const sectionData = sectionScores[sectionId];
      const isEliminationPassed =
        sectionData.score >= sectionData.eliminationScore;

      sectionResults.push({
        sectionId,
        sectionName: sectionData.sectionName,
        score: sectionData.score,
        maxScore: sectionData.maxScore,
        eliminationScore: sectionData.eliminationScore,
        isPassed: isEliminationPassed,
      });

      if (!isEliminationPassed && sectionData.eliminationScore > 0) {
        isPassed = false;
        failReason = `Điểm liệt section "${sectionData.sectionName}": ${sectionData.score}/${sectionData.eliminationScore}`;
      }
    });

    // 2. If no elimination, check overall pass score
    if (isPassed) {
      const passScore = testData?.Score || 0; // Điểm đạt của test
      if (totalScore < passScore && passScore > 0) {
        isPassed = false;
        failReason = `Điểm tổng không đạt: ${totalScore}/${passScore}`;
      }
    }

    console.log('📊 Section Results:', sectionResults);
    console.log(
      `🎯 Pass/Fail: ${isPassed ? 'PASS' : 'FAIL'} - ${
        failReason || 'Đạt yêu cầu'
      }`,
    );

    return {
      questionIds: answeredQuestions.join(','),
      answerIds: selectedAnswers.join(','),
      totalScore,
      correctAnswers,
      totalQuestions: allQuestions.length,
      answeredCount: answeredQuestions.length,
      isPassed,
      failReason,
      sectionResults,
      passScore: testData?.Score || 0,
    };
  }, [allQuestions, sections, exams, testData]);

  // Component for section overview modal
  const SectionOverview = useCallback(
    ({section, ref: modalRef}: {section: any; ref: any}) => {
      const {t} = useTranslation();

      // Get exams for current section
      const sectionExams = exams.filter(
        (exam: any) => exam.SectionId === section.Id,
      );

      const handleConfirmSubmit = () => {
        hideBottomSheet(modalRef);
        setTimeout(() => {
          confirmSectionSubmit();
        }, 100);
      };

      // Render question circles for each exam
      const renderExamQuestions = (exam: any, index: number) => {
        const questionIds = exam.QuestionId?.split(',') || [];
        const examQuestions = questionIds
          .map((questionId: string) =>
            allQuestions.find((q: any) => q.Id === questionId),
          )
          .filter(Boolean);

        return (
          <View key={exam.Id} style={{marginBottom: 16}}>
            <Text
              style={{
                ...TypoSkin.body2,
                color: ColorThemes.light.Neutral_Text_Color_Title,
                fontWeight: '600',
                marginBottom: 8,
                fontSize: 14,
                lineHeight: 18,
              }}>
              {index}. {exam.Name}
            </Text>
            <View
              style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                gap: 8,
              }}>
              {examQuestions.map((question: any, questionIndex: number) => {
                const userAnswers = userAnswersRef.current[question.Id];
                const isAnswered =
                  userAnswers &&
                  Object.values(userAnswers).some(
                    selected => selected === true,
                  );

                return (
                  <TouchableOpacity
                    key={question.Id}
                    onPress={() => jumpToQuestion(question.Id, exam)}
                    style={{
                      width: 32,
                      height: 32,
                      borderRadius: 16,
                      backgroundColor: isAnswered
                        ? ColorThemes.light.Warning_Color_Background
                        : ColorThemes.light.transparent,
                      borderWidth: 1,
                      borderColor: isAnswered
                        ? ColorThemes.light.Warning_Color_Main
                        : ColorThemes.light.Neutral_Border_Color_Main,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.body3,
                        color: isAnswered
                          ? ColorThemes.light.Warning_Color_Main
                          : ColorThemes.light.Neutral_Text_Color_Body,
                        fontWeight: '600',
                      }}>
                      {questionIndex + 1}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        );
      };

      // Calculate total stats
      const totalQuestions = sectionExams.reduce((total, exam) => {
        const questionIds = exam.QuestionId?.split(',') || [];
        return total + questionIds.length;
      }, 0);

      const answeredCount = sectionExams.reduce((total, exam) => {
        const questionIds = exam.QuestionId?.split(',') || [];
        const examQuestions = questionIds
          .map((questionId: string) =>
            allQuestions.find((q: any) => q.Id === questionId),
          )
          .filter(Boolean);

        const answered = examQuestions.filter((question: any) => {
          const userAnswers = userAnswersRef.current[question.Id];
          return (
            userAnswers &&
            Object.values(userAnswers).some(selected => selected === true)
          );
        });

        return total + answered.length;
      }, 0);

      return (
        <View
          style={{
            height: 450,
            width: '100%',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
            paddingHorizontal: 16,
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingVertical: 8,
              marginBottom: 16,
              paddingHorizontal: 12,
              borderRadius: 8,
            }}>
            <Text
              style={{
                ...TypoSkin.buttonText5,
                color: ColorThemes.light.Success_Color_Main,
              }}>
              ✅ Đã trả lời: {answeredCount}
            </Text>
            <Text
              style={{
                ...TypoSkin.buttonText5,
                color: ColorThemes.light.Error_Color_Main,
              }}>
              ❌ Chưa trả lời: {totalQuestions - answeredCount}
            </Text>
            <Text
              style={{
                ...TypoSkin.buttonText5,
                color: ColorThemes.light.Neutral_Text_Color_Body,
              }}>
              📝 Tổng: {totalQuestions}
            </Text>
          </View>

          <ScrollView style={{flex: 1}} showsVerticalScrollIndicator={false}>
            {sectionExams.map((exam, index) =>
              renderExamQuestions(exam, index + 1),
            )}
          </ScrollView>

          <View style={{paddingTop: 16, gap: 12}}>
            <AppButton
              title={t('exam.submitSection')}
              backgroundColor={ColorThemes.light.Primary_Color_Main}
              borderColor="transparent"
              containerStyle={{
                height: 45,
                borderRadius: 8,
              }}
              onPress={handleConfirmSubmit}
              textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
            />
            <AppButton
              title={t('exam.continueExam')}
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              containerStyle={{
                height: 45,
                borderRadius: 8,
                marginBottom: 16,
              }}
              onPress={() => {
                hideBottomSheet(modalRef);
              }}
              textColor={ColorThemes.light.Neutral_Text_Color_Body}
            />
          </View>
        </View>
      );
    },
    [
      exams,
      allQuestions,
      userAnswersRef,
      confirmSectionSubmit,
      jumpToQuestion,
      t,
    ],
  );

  // Function to create detailed result data for results screen
  const createDetailedResultData = useCallback(() => {
    const basicResult = collectAnswersForSubmit();

    // Create detailed sections with exams and questions
    const detailedSections = sections.map((section: any) => {
      // Get exams for this section
      const sectionExams = exams.filter(
        (exam: any) => exam.SectionId === section.Id,
      );

      // Calculate section statistics
      let sectionScore = 0;
      let sectionMaxScore = 0;
      let sectionCorrectQuestions = 0;
      let sectionTotalQuestions = 0;
      let sectionAnsweredQuestions = 0;

      // Create detailed exams
      const detailedExams = sectionExams
        .map((exam: any) => {
          const examQuestionIds = exam.QuestionId?.split(',') || [];

          // Get questions for this exam
          const examQuestions = examQuestionIds
            .map((questionId: string) => {
              const question = allQuestions.find(
                (q: any) => q.Id === questionId,
              );
              if (!question) return null;

              const userAnswers = userAnswersRef.current[questionId] || {};
              const selectedAnswerIds = Object.keys(userAnswers).filter(
                answerId => userAnswers[answerId] === true,
              );

              // Get correct answers
              const correctAnswerIds =
                question.lstAnswer
                  ?.filter(
                    (answer: any) =>
                      answer.IsResult === true ||
                      answer.IsResult === 1 ||
                      answer.IsResult === '1',
                  )
                  ?.map((answer: any) => answer.Id) || [];

              // Check if question is answered and correct
              const isAnswered = selectedAnswerIds.length > 0;
              const isCorrect =
                isAnswered &&
                (question.SelectionType === 1
                  ? selectedAnswerIds.length === 1 &&
                    correctAnswerIds.includes(selectedAnswerIds[0])
                  : selectedAnswerIds.length === correctAnswerIds.length &&
                    selectedAnswerIds.every((id: string) =>
                      correctAnswerIds.includes(id),
                    ) &&
                    correctAnswerIds.every((id: string) =>
                      selectedAnswerIds.includes(id),
                    ));

              // Update section statistics
              sectionTotalQuestions++;
              sectionMaxScore += question.Score || 1;
              if (isAnswered) sectionAnsweredQuestions++;
              if (isCorrect) {
                sectionCorrectQuestions++;
                sectionScore += question.Score || 1;
              }

              return {
                id: question.Id,
                // content: question.Content || question.Name || 'No content',
                // selectionType: question.SelectionType,
                // score: question.Score || 1,
                // isAnswered,
                isCorrect,
              };
            })
            .filter(Boolean);

          return {
            id: exam.Id,
            name: exam.Name || `Exam ${exam.Sort || 1}`,
            sort: exam.Sort || 1,
            // totalQuestions: examTotalQuestions,
            // answeredQuestions: examAnsweredQuestions,
            // correctQuestions: examCorrectQuestions,
            // score: examScore,
            // maxScore: examMaxScore,
            questions: examQuestions,
          };
        })
        .sort((a: any, b: any) => a.sort - b.sort);

      // Find section result from basic result
      const sectionResult = basicResult.sectionResults.find(
        (sr: any) => sr.sectionId === section.Id,
      );

      return {
        id: section.Id,
        name: section.Name || `Section ${section.Sort || 1}`,
        score: sectionScore,
        maxScore: sectionMaxScore,
        eliminationScore: section.Score || 0,
        isPassed: sectionResult?.isPassed || false,
        totalQuestions: sectionTotalQuestions,
        answeredQuestions: sectionAnsweredQuestions,
        correctQuestions: sectionCorrectQuestions,
        exams: detailedExams,
      };
    });

    // Create final result object
    const detailedResult = {
      // Basic exam info
      testId: testId,
      testName: testData?.Name || 'Exam',
      completedAt: new Date().toISOString(),

      // Overall scores
      totalScore: basicResult.totalScore,
      maxTotalScore: detailedSections.reduce(
        (sum: number, section: any) => sum + section.maxScore,
        0,
      ),
      passScore: basicResult.passScore,
      isPassed: basicResult.isPassed,
      failReason: basicResult.failReason,
      answeredCount: basicResult.answeredCount,
      // Question statistics
      totalQuestions: basicResult.totalQuestions,
      answeredQuestions: basicResult.answeredCount,
      correctAnswers: basicResult.correctAnswers,

      // For API submission
      questionIds: basicResult.questionIds,
      answerIds: basicResult.answerIds,

      // Detailed breakdown for results screen
      sections: detailedSections,
    };

    console.log('📊 Detailed Result Data:', detailedResult);
    return detailedResult;
  }, [
    collectAnswersForSubmit,
    sections,
    exams,
    allQuestions,
    testData,
    testId,
    userAnswersRef,
  ]);

  // Sound reference
  const soundRef = useRef<Sound | null>(null);

  // Helper function to get audio URL
  const getAudioUrl = (audioPath: string) => {
    return audioPath?.includes('http')
      ? audioPath
      : `${ConfigAPI.url.replace('/api/', '')}${audioPath}`;
  };

  // Function to play audio
  const playAudio = (audioUrl: string) => {
    // If same audio is playing, pause it
    if (currentPlayingAudio === audioUrl && !isPaused) {
      pauseAudio();
      return;
    }

    // If same audio is paused, resume it
    if (currentPlayingAudio === audioUrl && isPaused) {
      resumeAudio();
      return;
    }

    // Stop any currently playing sound
    stopAudio();

    // Set current playing audio
    setCurrentPlayingAudio(audioUrl);
    setIsPaused(false);
    setCurrentTime(0);

    // Set up sound
    Sound.setCategory('Playback');

    // Create new sound instance
    const sound = new Sound(audioUrl, '', error => {
      if (error) {
        console.log('Failed to load sound', error);
        setCurrentPlayingAudio(null);
        setIsPaused(false);
        return;
      }

      // Play the sound
      sound.play(success => {
        if (success) {
          console.log('Sound played successfully');
        } else {
          console.log('Sound playback failed');
        }
        // Reset playing state when audio finishes
        setCurrentPlayingAudio(null);
        setIsPaused(false);
        setCurrentTime(0);
      });
    });

    // Save reference to sound
    soundRef.current = sound;
  };

  // Function to pause audio
  const pauseAudio = () => {
    if (soundRef.current && currentPlayingAudio) {
      soundRef.current.getCurrentTime((seconds) => {
        setCurrentTime(seconds);
        soundRef.current?.pause();
        setIsPaused(true);
        console.log('Audio paused at:', seconds);
      });
    }
  };

  // Function to resume audio
  const resumeAudio = () => {
    if (soundRef.current && currentPlayingAudio && isPaused) {
      soundRef.current.setCurrentTime(currentTime);
      soundRef.current.play((success) => {
        if (success) {
          console.log('Audio resumed from:', currentTime);
        } else {
          console.log('Audio resume failed');
        }
        // Reset playing state when audio finishes
        setCurrentPlayingAudio(null);
        setIsPaused(false);
        setCurrentTime(0);
      });
      setIsPaused(false);
    }
  };

  // Function to stop audio
  const stopAudio = () => {
    if (soundRef.current) {
      soundRef.current.stop();
      soundRef.current.release();
      soundRef.current = null;
    }
    setCurrentPlayingAudio(null);
    setIsPaused(false);
    setCurrentTime(0);
  };

  useEffect(() => {
    // Cleanup function to release sound resources when component unmounts
    return stopAudio();
  }, []);

  if (loading) {
    return (
      <SafeAreaView
        style={{
          flex: 1,
          backgroundColor:
            ColorThemes.light.Neutral_Background_Color_Absoluteary_Color_Main,
        }}>
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
          <ActivityIndicator color={ColorThemes.light.Primary_Color_Main} />
          <Text style={{marginTop: 16, ...TypoSkin.body2}}>
            {t('common.loading')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />

      {/* Header with timer and submit button */}
      <ScreenHeader
        style={{paddingTop: Platform.OS === 'ios' ? 0 : 24}}
        action={
          <AppButton
            title={t('exam.submit')}
            containerStyle={{
              justifyContent: 'flex-start',
              alignSelf: 'baseline',
              paddingRight: 16,
            }}
            backgroundColor={'transparent'}
            textStyle={{
              ...TypoSkin.buttonText2,
              color: ColorThemes.light.Primary_Color_Main,
            }}
            borderColor="transparent"
            onPress={() => {
              handleSectionSubmit();
            }}
            textColor={ColorThemes.light.infor_main_color}
          />
        }
        title={
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              paddingBottom: 8,
            }}>
            <Text
              style={{
                ...TypoSkin.title3,
                fontWeight: '700',
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}>
              {(() => {
                const answeredCount = Object.keys(
                  userAnswersRef.current,
                ).filter(questionId => {
                  const answers = userAnswersRef.current[questionId];
                  return (
                    answers &&
                    Object.values(answers).some(selected => selected === true)
                  );
                }).length;
                return `${answeredCount}/${exams
                  .map((exam: any) => exam.QuestionId?.split(',').length || 0)
                  .reduce((acc, curr) => acc + curr, 0)}`;
              })()}
            </Text>
          </View>
        }
        backIcon={
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
            <Winicon src="outline/arrows/left-arrow" size={20} />
            <AnimatedTimerIcon
              shouldRotate={isHalfTimeReached}
              source={require('../../../assets/images/timer.png')}
              style={{width: 20, height: 20}}
              imgStyle={{width: 20, height: 20}}
            />
            <CountdownTimer
              key={sectionTimerKey} // Force reset when section changes
              textStyle={{
                ...TypoSkin.title3,
                fontWeight: '700',
                color: ColorThemes.light.neutral_text_title_color,
              }}
              initialMinutes={currentSectionTime}
              onHalfTimeReached={handleHalfTimeReached}
              onTimeUp={() => {
                // Auto submit current section when time is up
                handleSectionSubmit();
              }}
            />
          </View>
        }
        onBack={() => {
          navigateBack();
          stopAudio();
        }}
      />

      {/* Section and Exam Selection */}
      <View
        style={{
          backgroundColor: ColorThemes.light.neutral_main_background_color,
          borderBottomWidth: 1,
          borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
        }}>
        {/* Section Selector */}
        {sections.length > 1 && (
          <ScrollView
            ref={scrollSectionRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{}}
            style={{
              marginBottom: 8,
              borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
              borderBottomWidth: 0.1,
            }}>
            {sections.map((section, index) => {
              const isSubmitted = submittedSections.has(section.Id);
              const isCurrentSection = selectedSection?.Id === section.Id;
              const currentIndex = sections.findIndex(
                s => s.Id === selectedSection?.Id,
              );
              // Only allow access to current section or next section if current is submitted
              const canAccess =
                index === currentIndex ||
                (index === currentIndex + 1 &&
                  submittedSections.has(sections[currentIndex]?.Id));

              return (
                <TouchableOpacity
                  key={section.Id}
                  onPress={() =>
                    canAccess ? handleSectionChange(section) : null
                  }
                  disabled={!canAccess}
                  style={{
                    paddingHorizontal: 12,
                    paddingVertical: 4,
                    backgroundColor:
                      ColorThemes.light
                        .Neutral_Background_Color_Absoluteary_Color_Main,
                    borderBottomWidth: 1.5,
                    borderBottomColor: isCurrentSection
                      ? ColorThemes.light.Primary_Color_Main
                      : ColorThemes.light.Neutral_Border_Color_Main,
                    opacity: canAccess ? 1 : 0.5,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 8,
                    }}>
                    <Text
                      style={{
                        ...TypoSkin.body2,
                        fontWeight: isCurrentSection ? '700' : '400',
                        color: isCurrentSection
                          ? ColorThemes.light.Primary_Color_Main
                          : canAccess
                          ? ColorThemes.light.neutral_text_body_color
                          : ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}>
                      {section.Name} ({section.totalQuestions})
                    </Text>
                    {isSubmitted && (
                      <Winicon
                        src="fill/user interface/check"
                        size={16}
                        color={ColorThemes.light.Success_Color_Main}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        )}

        {/* Exam Info */}
        {selectedSection &&
          (() => {
            const sectionExams = exams.filter(
              e => e.SectionId === selectedSection.Id,
            );
            const totalExams = sectionExams.length;

            return (
              <ScrollView
                style={{
                  height: 'auto',
                  maxHeight: Dimensions.get('window').height / 2.5,
                }}>
                {/* Exam Title */}
                <View
                  style={{
                    alignItems: 'center',
                    paddingVertical: 8,
                    paddingHorizontal: 16,
                  }}>
                  <Text
                    style={{
                      ...TypoSkin.title3,
                      color: ColorThemes.light.neutral_text_title_color,
                      textAlign: 'center',
                      fontFamily: 'Noto Sans JP',
                    }}>
                    {selectedExam?.Name ||
                      `${t('exam.exam')} ${currentExamIndex + 1}`}
                  </Text>
                </View>

                {/* Audio Player */}
                {selectedExam?.Audio && (
                  <View
                    style={{
                      paddingHorizontal: 16,
                      paddingVertical: 8,
                      backgroundColor:
                        ColorThemes.light.neutral_main_background_color,
                      borderRadius: 8,
                      marginHorizontal: 16,
                      marginBottom: 8,
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (selectedExam?.Audio) {
                          const audioUrl = getAudioUrl(selectedExam.Audio);
                          playAudio(audioUrl);
                        }
                      }}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <Winicon
                        src={
                          currentPlayingAudio === getAudioUrl(selectedExam?.Audio || '') && !isPaused
                            ? 'color/multimedia/button-pause'
                            : currentPlayingAudio === getAudioUrl(selectedExam?.Audio || '') && isPaused
                            ? 'color/multimedia/button-play'
                            : 'outline/multimedia/sound'
                        }
                        size={16}
                        color={ColorThemes.light.Info_Color_Main}
                      />
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color: ColorThemes.light.Info_Color_Main,
                          marginLeft: 8,
                          fontWeight: '600',
                        }}>
                        {currentPlayingAudio === getAudioUrl(selectedExam?.Audio || '') && !isPaused
                          ? 'Đang nghe (Bấm để tạm dừng)'
                          : currentPlayingAudio === getAudioUrl(selectedExam?.Audio || '') && isPaused
                          ? 'Đã tạm dừng (Bấm để tiếp tục)'
                          : 'Bấm để nghe'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}

                {/* Collapsible Image */}
                {selectedExam?.Img && (
                  <View
                    style={{
                      marginHorizontal: 16,
                      marginBottom: 8,
                      borderRadius: 8,
                      overflow: 'hidden',
                      backgroundColor:
                        ColorThemes.light.neutral_main_background_color,
                    }}>
                    <View style={{padding: 12}}>
                      <ClickableImage
                        source={{uri: ConfigAPI.urlImg + selectedExam.Img}}
                        style={{
                          width: '100%',
                          height: 150,
                          borderRadius: 8,
                        }}
                        resizeMode="contain"
                      />
                    </View>
                  </View>
                )}

                {/* PDF Document */}
                {selectedExam?.Pdf && (
                  <View
                    style={{
                      marginHorizontal: 8,
                      marginBottom: 8,
                      borderRadius: 8,
                      overflow: 'hidden',
                      backgroundColor:
                        ColorThemes.light.neutral_main_background_color,
                    }}>
                    <View style={{padding: 12}}>
                      <Text
                        style={{
                          ...TypoSkin.subtitle3,
                          color: ColorThemes.light.Neutral_Text_Color_Title,
                          marginBottom: 8,
                        }}>
                        📄 {t('exam.examDocument')}
                      </Text>
                      <View
                        style={{
                          height: 400,
                          borderRadius: 8,
                          overflow: 'hidden',
                          // Ensure PDF container can handle nested scrolling
                          backgroundColor: '#f5f5f5',
                        }}
                        onStartShouldSetResponder={() => true}
                        onMoveShouldSetResponder={() => true}>
                        <PDFViewer
                          url={selectedExam.Pdf}
                          fileName={`${t('exam.exam')} ${currentExamIndex + 1} - Document`}
                          height={400}
                          maxFileSize={15}
                          enableOptimization={true}
                          useGoogleViewer={true}
                          onError={(error) => {
                            console.error('Exam PDF error:', error);
                          }}
                          onLoadStart={() => {
                            console.log('Exam PDF loading started');
                          }}
                          onLoadEnd={() => {
                            console.log('Exam PDF loading completed');
                          }}
                        />
                      </View>
                    </View>
                  </View>
                )}
              </ScrollView>
            );
          })()}
      </View>

      {/* Questions Content */}
      <FlatList
        ref={questionsListRef}
        data={currentQuestions}
        keyExtractor={item => item.Id}
        renderItem={({item, index}) => (
          <QuestionItem
            question={item}
            questionIndex={index}
            onAnswerSelect={handleAnswerSelect}
            onPlayAudio={playAudio}
            currentPlayingAudio={currentPlayingAudio}
            isPaused={isPaused}
            t={t}
          />
        )}
        contentContainerStyle={{paddingBottom: 50}}
        removeClippedSubviews={true}
        maxToRenderPerBatch={5}
        windowSize={10}
        initialNumToRender={3}
        getItemLayout={(_, index) => ({
          length: 200, // Estimated item height
          offset: 200 * index,
          index,
        })}
        onScrollToIndexFailed={info => {
          console.log('❌ ScrollToIndex failed:', info);
          // Fallback: scroll to offset
          const wait = new Promise(resolve => setTimeout(resolve, 500));
          wait.then(() => {
            questionsListRef.current?.scrollToOffset({
              offset: info.index * 200,
              animated: true,
            });
          });
        }}
      />

      {/* Footer with Navigation and Question Counter */}
      <View
        style={{
          backgroundColor:
            ColorThemes.light.Neutral_Background_Color_Absoluteary_Color_Main,
          borderTopWidth: 1,
          borderTopColor: ColorThemes.light.Neutral_Border_Color_Main,
          paddingHorizontal: 16,
          paddingVertical: 12,
        }}>
        {/* Navigation Buttons */}
        {selectedSection &&
          (() => {
            const sectionExams = exams.filter(
              e => e.SectionId === selectedSection.Id,
            );
            const totalExams = sectionExams.length;
            const showNavigation = totalExams > 1;
            const showBackButton = showNavigation && currentExamIndex > 0;
            const showNextButton =
              showNavigation && currentExamIndex < totalExams - 1;

            if (!showNavigation) {
              return null;
            }

            return (
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                {/* Back Button */}
                {totalExams > 1 ? (
                  <AppButton
                    // title={t('exam.previousExam')}
                    backgroundColor={
                      showBackButton
                        ? ColorThemes.light.Primary_Color_Main
                        : ColorThemes.light.Neutral_Background_Color_Disable
                    }
                    textStyle={{
                      ...TypoSkin.buttonText3,
                      color:
                        ColorThemes.light.Neutral_Background_Color_Absolute,
                    }}
                    borderColor={ColorThemes.light.Neutral_Border_Color_Main}
                    prefixIcon={'outline/arrows/left-arrow'}
                    prefixIconSize={16}
                    onPress={!showBackButton ? () => {} : handlePreviousExam}
                    textColor={
                      ColorThemes.light.Neutral_Background_Color_Absolute
                    }
                    containerStyle={{
                      paddingHorizontal: 12,
                      marginRight: 8,
                      borderRadius: 8,
                    }}
                  />
                ) : null}
                {totalExams > 1 ? (
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      justifyContent: 'center',
                    }}>
                    <ScrollView
                      horizontal
                      showsHorizontalScrollIndicator={false}
                      contentContainerStyle={{
                        gap: 4,
                        justifyContent: 'center',
                        flexGrow: 1,
                      }}>
                      {sectionExams.map((exam, index) => (
                        <View
                          key={exam.Id}
                          style={{
                            width: 32,
                            height: 32,
                            backgroundColor:
                              currentExamIndex === index
                                ? ColorThemes.light.Primary_Color_Main
                                : ColorThemes.light.Neutral_Border_Color_Main,
                            borderRadius: 100,
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                          <Text
                            style={{
                              ...TypoSkin.body2,
                              color:
                                currentExamIndex === index
                                  ? ColorThemes.light
                                      .Neutral_Background_Color_Absolute
                                  : ColorThemes.light.Neutral_Text_Color_Body,
                              ...(currentExamIndex === index && {
                                fontWeight: '700',
                              }),
                            }}>
                            {index + 1}
                          </Text>
                        </View>
                      ))}
                    </ScrollView>
                  </View>
                ) : null}
                {/* Next Button */}
                {totalExams > 1 ? (
                  <AppButton
                    // title={t('exam.nextExam')}
                    backgroundColor={
                      showNextButton
                        ? ColorThemes.light.Primary_Color_Main
                        : ColorThemes.light.Neutral_Background_Color_Disable
                    }
                    textStyle={{
                      ...TypoSkin.buttonText3,
                      color:
                        ColorThemes.light.Neutral_Background_Color_Absolute,
                    }}
                    borderColor="transparent"
                    suffixIcon={'outline/arrows/right-arrow'}
                    suffixIconSize={16}
                    onPress={!showNextButton ? () => {} : handleNextExam}
                    textColor={
                      ColorThemes.light.Neutral_Background_Color_Absolute
                    }
                    containerStyle={{
                      paddingHorizontal: 12,
                      marginLeft: 8,
                      borderRadius: 8,
                    }}
                  />
                ) : null}
              </View>
            );
          })()}
      </View>
    </SafeAreaView>
  );
}

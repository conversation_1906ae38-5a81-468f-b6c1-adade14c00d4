import {useDispatch} from 'react-redux';
import {
  setData,
  startGame,
  nextQuestion,
  nextLevel,
  restartLevel,
} from '../reducers/sakuTcReducer';
import {
  initData,
  loadGameConfig,
  loadGameQuestions,
} from '../../asyncThunk/sakuTcAsyncThunk';

export const useSakuTcHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    startGame: () => {
      dispatch(startGame());
    },
    nextQuestion: () => {
      dispatch(nextQuestion());
    },
    nextLevel: () => {
      dispatch(nextLevel());
    },
    restartLevel: () => {
      dispatch(restartLevel());
    },
    initData: async ({
      gameId,
      stage,
      competenceId,
    }: {
      gameId: string;
      stage: number;
      competenceId: string;
    }) => {
      return dispatch(initData({gameId, stage, competenceId}) as any);
    },
    loadGameConfig: async ({gameId}: {gameId: string}) => {
      return dispatch(loadGameConfig({gameId}) as any);
    },
    loadGameQuestions: async ({
      gameId,
      stage,
      competenceId,
    }: {
      gameId: string;
      stage: number;
      competenceId: string;
    }) => {
      return dispatch(loadGameQuestions({gameId, stage, competenceId}) as any);
    },
  };

  return action;
};

import React, {useState, useEffect, useCallback} from 'react';
import {
  ScrollView,
  TouchableOpacity,
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {Winicon} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';
import EmptyPage from '../../../Screen/emptyPage';
import {useTranslation} from 'react-i18next';

interface VideoItem {
  Id: number;
  Name: string;
  Url: string;
  isCompleted?: boolean;
}

interface VideoListTabProps {
  videos: VideoItem[];
  videoLoading: boolean;
  currentVideoIndex: number;
  onVideoSelect: (index: number, video: VideoItem) => void;
  Step: any;
}

export default function VideoListTab({
  videos,
  videoLoading,
  currentVideoIndex,
  onVideoSelect,
}: VideoListTabProps) {
  const {t} = useTranslation();

  const renderVideoItem = (video: VideoItem, index: number) => {
    const isActive = index === currentVideoIndex;
    const isCompleted = video.isCompleted;

    return (
      <TouchableOpacity
        key={`video-list-item-${video.Id || 'unknown'}-index-${index}`}
        style={[styles.videoItem, isActive && styles.activeVideoItem]}
        onPress={() => onVideoSelect(index, video)}>
        <View style={styles.videoThumbnail}>
          {isActive ? (
            <Winicon
              src="color/multimedia/btn-play"
              size={24}
              color={ColorThemes.light.Primary_Color_Main}
            />
          ) : (
            <Winicon
              src="color/multimedia/button-play"
              size={24}
              color={ColorThemes.light.Primary_Color_Main}
            />
          )}
        </View>
        <View style={styles.videoInfo}>
          <Text
            style={[styles.videoTitle, isActive && styles.activeVideoTitle]}
            numberOfLines={2}>
            {video.Name}
          </Text>
          {/* <Text style={styles.videoSubtitle}>
            {formatFileSize(video.Size)}
            {video.duration ? ` • ${video.duration}` : ''}
          </Text> */}
        </View>

        <View style={styles.videoActions}>
          {isCompleted && (
            <View style={styles.completedIcon}>
              <Winicon
                src="fill/user interface/check"
                size={16}
                color={ColorThemes.light.Success_Color_Main}
              />
            </View>
          )}
          {isActive && (
            <View style={styles.playingIndicator}>
              <Winicon
                src="fill/media/volume-2"
                size={16}
                color={ColorThemes.light.Primary_Color_Main}
              />
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (videoLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator
          size="large"
          color={ColorThemes.light.Primary_Color_Main}
        />
        <Text style={styles.loadingText}>{t('course.loadingVideos')}</Text>
      </View>
    );
  }

  if (videos.length === 0) {
    return (
      <EmptyPage
        title={t('course.noVideos')}
        subtitle={t('course.noVideosDescription')}
        icon="outline/media/video-off"
      />
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.headerSubtitle}>
          {videos.filter(v => v.isCompleted).length}/{videos.length}{' '}
          {t('course.completed')}
        </Text>
      </View>

      <View style={styles.videoList}>
        {videos.map((video, index) => renderVideoItem(video, index))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginTop: 8,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  headerSubtitle: {
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginTop: 4,
  },
  videoList: {
    padding: 16,
  },
  videoItem: {
    flexDirection: 'row',
    padding: 12,
    marginBottom: 8,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  activeVideoItem: {
    borderColor: ColorThemes.light.Primary_Color_Main,
    backgroundColor: ColorThemes.light.Primary_Color_Background_color,
  },
  videoThumbnail: {
    position: 'relative',
    width: 30,
    height: 60,
    borderRadius: 6,
    overflow: 'hidden',
    marginRight: 12,
    alignContent: 'center',
    justifyContent: 'center',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  playOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  durationBadge: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  durationText: {
    ...TypoSkin.label3,
    color: '#fff',
    fontSize: 10,
  },
  videoInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  videoTitle: {
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 4,
    fontSize: 14,
  },
  activeVideoTitle: {
    color: ColorThemes.light.Primary_Color_Main,
    fontWeight: '600',
  },
  videoSubtitle: {
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  videoActions: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 32,
  },
  completedIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: ColorThemes.light.Success_Color_Background_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playingIndicator: {
    marginTop: 4,
  },
});

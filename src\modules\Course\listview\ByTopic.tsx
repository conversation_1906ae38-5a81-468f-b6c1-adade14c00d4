/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-hooks/exhaustive-deps */
import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {AppButton} from 'wini-mobile-components';
import {CourseDA} from '../da';
import {DefaultProduct} from '../../Default/card/defaultProduct';
import {useTranslation} from 'react-i18next';
import {navigate, RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import {DataController} from '../../../base/baseController';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
  id: string;
}

export default function ByTopic(props: Props) {
  const [data, setData] = useState<Array<any>>([]);

  const [isLoading, setLoading] = useState(true);
  const [isRefresh, setRefresh] = useState(false);
  const [isLoadMore, setLoadMore] = useState(false);
  const [listItems, setlistItems] = useState<Array<any>>([]);
  const {t} = useTranslation();
  const courseDA = new CourseDA();
  var page = 1;
  const size = 10;

  useEffect(() => {
    onData();
  }, []);

  const onData = async () => {
    setLoading(true);
    const result = await courseDA.getAllListbyTopic(page, size, props.id);

    if (result) {
      var lst = await Promise.all(
        result.data.map(async (item: {Id: string}) => ({
          id: item.Id,
          icon: 'outline/education/hat-3',
          title: `${await courseDA.countStudentCourse(item.Id)} ${t(
            'student',
          )}`,
        })),
      );
      setlistItems(lst);
      setData(result.data);
      setLoading(false);
      setRefresh(false);
    }
  };

  const onRefresh = async () => {
    setRefresh(true);
    await onData();
  };

  const handleLoadMore = async () => {
    page++;
    const result = await courseDA.getAllListbyTopic(page, size, props.id);
    if (result.code === 200) {
      var lst = data.push(result.data);
      setData([lst]);
      setLoadMore(false);
    }
  };

  return (
    <View
      style={{
        width: '100%',
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FlatList
        data={data}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        ListHeaderComponent={() => {
          return props.titleList ? (
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'space-between',
                paddingHorizontal: 16,
                flexDirection: 'row',
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Absolute,
              }}>
              <Text
                style={{
                  ...TypoSkin.heading5,
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}>
                {props.titleList}
              </Text>
            </View>
          ) : null;
        }}
        contentContainerStyle={{
          gap: 20,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        renderItem={({item, index}) => {
          return (
            <DefaultProduct
              key={index}
              onPressDetail={() => {
                navigate(RootScreen.CourseDetail, {id: item.Id});
              }}
              titleStyle={{
                ...TypoSkin.heading7,
                color: ColorThemes.light.Neutral_Text_Color_Body,
                paddingTop: 4,
              }}
              priceStyle={{
                ...TypoSkin.heading7,
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}
              listItems={listItems.filter(t => t.id === item.Id)}
              flexDirection="row"
              imgStyle={{
                width: 117,
                height: 117,
              }}
              noDivider
              containerStyle={{
                paddingHorizontal: 16,
                width: Dimensions.get('window').width - 24,
              }}
              data={item}
              onPressLikeAction={() => {}}
            />
          );
        }}
        style={{width: '100%', height: '100%'}}
        keyExtractor={item => item.Id?.toString()}
        horizontal={false}
        onEndReachedThreshold={0.5}
        // onEndReached={handleLoadMore}
        ListFooterComponent={() => {
          return <View style={{height: 16}} />;
        }}
        ListEmptyComponent={() => {
          if (isLoading) {
            return [1, 2, 3].map((_, index) => (
              <SkeletonPlaceCard key={`skeleton-${index}`} />
            ));
          }
          if (isLoadMore) {
            return (
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                }}>
                <ActivityIndicator
                  color={ColorThemes.light.Primary_Color_Main}
                />
              </View>
            );
          }
          return <EmptyPage title={t('nodata')} />;
        }}
      />
    </View>
  );
}

function SkeletonPlaceCard() {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View
        style={{
          padding: 16,
          marginBottom: 16,
          backgroundColor: ColorThemes.light.white,
        }}>
        <View
          style={{
            flexDirection: 'row',
            gap: 12,
          }}>
          {/* Course image placeholder */}
          <View
            style={{
              width: 117,
              height: 117,
              borderRadius: 8,
            }}
          />

          <View style={{flex: 1, gap: 8}}>
            {/* Title placeholder */}
            <View
              style={{
                width: '80%',
                height: 16,
                borderRadius: 4,
              }}
            />

            {/* Price placeholder */}
            <View
              style={{
                width: '40%',
                height: 16,
                borderRadius: 4,
                marginTop: 4,
              }}
            />

            {/* Course info placeholder */}
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: 8,
                gap: 8,
              }}>
              <View
                style={{
                  width: 20,
                  height: 20,
                  borderRadius: 10,
                }}
              />
              <View
                style={{
                  width: '60%',
                  height: 12,
                  borderRadius: 4,
                }}
              />
            </View>
          </View>
        </View>
      </View>
    </SkeletonPlaceholder>
  );
}

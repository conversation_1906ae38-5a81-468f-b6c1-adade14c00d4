/* eslint-disable react/no-unstable-nested-components */

/* eslint-disable react-native/no-inline-styles */
import {
  Animated,
  Dimensions,
  RefreshControl,
  StyleSheet,
  View,
} from 'react-native';
import React, {useRef, useState, useEffect} from 'react';
import {ColorThemes} from '../../../assets/skin/colors';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  ComponentStatus,
  FBottomSheet,
  hideBottomSheet,
  showBottomSheet,
  showSnackbar,
} from 'wini-mobile-components';
import {useRoute} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../redux/store/store';
import ImagePicker from 'react-native-image-crop-picker';
import {BaseDA} from '../../../base/BaseDA';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {FollowStatus} from '../../../Config/Contanst';
import About from '../groups/about';
import {CustomerDA} from '../../customer/da';
import {ActionSheetOption} from '../components/ActionSheet';
import ActionSheet from '../components/ActionSheet';
import {
  PostsTab,
  SkeletonGroupIndex,
  ProfileHeader,
  ProfileOriginalHeader,
  ProfileTabBar,
} from '../modules/ProfileIndexModule';

export default function ProfileCommunity() {
  const [isLoading, setIsLoading] = useState(false);
  const [isRefresh, setIsRefresh] = useState(false);

  const handleMainRefresh = () => {
    setIsRefresh(true);
    getProfile();
  };
  const [activeTab, setActiveTab] = useState(0);
  const route = useRoute<any>();
  const {Id, forEschool} = route.params;
  const scrollY = useRef(new Animated.Value(0)).current;
  const customerDA = new CustomerDA();
  const [profile, setProfile] = useState<any>(null);
  const [isHeaderVisible, setIsHeaderVisible] = useState(false);
  const currentUser = useSelectorCustomerState().data;
  const bottomSheetRef = useRef<any>(null);
  const dispatch: AppDispatch = useDispatch();
  const [checkFollowCustomer, setCheckFollowCustomer] = useState<any>(null);
  const [avt, setAvt] = useState<any>(null);
  const [currentActions, setCurrentActions] = useState<ActionSheetOption[]>([]);

  useEffect(() => {
    getProfile();
  }, [Id, isRefresh]);

  const getProfile = async () => {
    if (!Id) return;
    setIsLoading(true);
    try {
      setProfile(null);
      const result = await customerDA.getCustomerbyId(Id);
      if (result.code === 200) {
        const checkFollowCustomer = await customerDA.checkFollowCustomer(Id);
        setCheckFollowCustomer(checkFollowCustomer);
        setProfile({
          ...result.data,
          isFollowing: checkFollowCustomer ? checkFollowCustomer.Status : 0,
        });
      }
    } catch (error) {
      console.error('Failed to fetch group detail:', error);
    } finally {
      setIsLoading(false);
      setTimeout(() => {
        setIsRefresh(false);
      }, 500);
    }
  };

  useEffect(() => {
    if (profile) {
      setAvt(profile?.AvatarUrl);
    }
  }, [profile]);

  const openSheetWithActions = (actions: ActionSheetOption[]) => {
    setCurrentActions(actions);
    showBottomSheet({
      ref: bottomSheetRef,
      enableDismiss: true,
      title: 'Actions',
      children: (
        <View style={styles.bottomSheetContainer}>
          <ActionSheet actions={actions} onSelect={handleSelect} />
        </View>
      ),
    });
  };

  const pickerImg = async () => {
    const img = await ImagePicker.openPicker({
      multiple: false,
      cropping: true,
      cropperCircleOverlay: true,
    });
    if (img) {
      const resImgs = await BaseDA.uploadFiles([
        {
          uri: img.path,
          type: img.mime,
          name: img.filename ?? 'new file img',
        },
      ]);
      if (resImgs) {
        await dispatch(
          CustomerActions.edit({
            ...currentUser,
            AvatarUrl: resImgs[0].Id,
          }),
        ).then((res: any) => {
          if (res.code === 200) {
            setAvt(resImgs[0].Id);
            showSnackbar({
              message: 'Cập nhật ảnh đại diện thành công',
              status: ComponentStatus.SUCCSESS,
              bottom: 60,
            });
            dispatch(CustomerActions.getInfor());
          }
        });
      }
    }
  };

  const handleSelect = (key: string) => {
    console.log('Selected:', key);
    switch (key) {
      case 'addfriend':
        customerDA.Acceptfollow(profile?.Id).then((res: any) => {
          if (res) {
            setProfile({...profile, isFollowing: FollowStatus.Accept});
          }
        });
        break;
      case 'unfriend':
        customerDA.unfollow(profile?.Id).then((res: any) => {
          if (res) {
            setProfile({...profile, isFollowing: FollowStatus.None});
          }
        });
        break;
      case 'cancelfriend':
        customerDA.unfollow(profile?.Id).then((res: any) => {
          if (res) {
            setProfile({...profile, isFollowing: FollowStatus.None});
          }
        });
        break;
      default:
        break;
    }
    hideBottomSheet(bottomSheetRef);
  };

  const headerHeight = 320;
  const tabBarHeight = 50;

  const newHeaderOpacity = scrollY.interpolate({
    inputRange: [headerHeight - 20, headerHeight],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const changeTab = (tab: any) => {
    setActiveTab(tab);
  };

  const tabs = [
    {Id: 0, Name: 'Posts'},
    {Id: 2, Name: 'About'},
  ];

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      {isLoading ? (
        <SkeletonGroupIndex />
      ) : (
        <>
          <FBottomSheet ref={bottomSheetRef} />

          <ProfileHeader
            profile={profile}
            currentUser={currentUser}
            avt={avt}
            forEschool={forEschool}
            isHeaderVisible={isHeaderVisible}
            newHeaderOpacity={newHeaderOpacity}
            onEditCover={pickerImg}
          />

          {/* Content  */}
          <Animated.ScrollView
            bounces={true}
            contentContainerStyle={[
              styles.scrollViewContent,
              {paddingTop: headerHeight + tabBarHeight},
            ]}
            scrollEventThrottle={16}
            refreshControl={
              <RefreshControl
                refreshing={isRefresh}
                onRefresh={handleMainRefresh}
                colors={[ColorThemes.light.Primary_Color_Main]}
                tintColor={ColorThemes.light.Primary_Color_Main}
              />
            }
            onScroll={Animated.event(
              [{nativeEvent: {contentOffset: {y: scrollY}}}],
              {
                useNativeDriver: true,
                listener: (event: any) => {
                  const offsetY = event.nativeEvent.contentOffset.y;
                  setIsHeaderVisible(offsetY > headerHeight - 30);
                },
              },
            )}>
            {/* Header gốc - biến mất khi cuộn */}
            <ProfileOriginalHeader
              profile={profile}
              currentUser={currentUser}
              avt={avt}
              forEschool={forEschool}
              onEditCover={pickerImg}
            />

            {/* Tabbar - di chuyển lên trên và được ghim */}
            <ProfileTabBar
              activeTab={activeTab}
              onTabChange={changeTab}
              forEschool={forEschool}
            />

            {activeTab === 0 && !forEschool ? (
              <PostsTab profile={profile} />
            ) : (
              <About data={profile} isProfile />
            )}
          </Animated.ScrollView>
        </>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  bottomSheetContainer: {
    height: Dimensions.get('window').height / 5,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  scrollViewContent: {
    // paddingTop is set inline due to dynamic value
  },
});

// Quản lý các bộ câu hỏi cho game Ai Là Triệu Phú
import generalQuestions from './questions';
import scienceQuestions from './questionsScience';
import { ALTPQuestion } from './questions';
import { categories } from './questionCategories';

// Interface cho bộ câu hỏi
export interface QuestionSet {
  id: string;
  name: string;
  description: string;
  categoryId: string;
  questions: ALTPQuestion[];
}

// Danh sách các bộ câu hỏi
export const questionSets: QuestionSet[] = [
  {
    id: 'general',
    name: '<PERSON><PERSON><PERSON> thức chung',
    description: 'Bộ câu hỏi về kiến thức chung, đời sống, xã hội',
    categoryId: 'general',
    questions: generalQuestions
  },
  {
    id: 'science',
    name: '<PERSON><PERSON><PERSON> học',
    description: 'Bộ câu hỏi về khoa học, vật lý, hóa học, sinh học',
    categoryId: 'science',
    questions: scienceQuestions
  }
];

// Hàm lấy bộ câu hỏi theo ID
export const getQuestionSetById = (id: string): QuestionSet | undefined => {
  return questionSets.find(set => set.id === id);
};

// Hàm lấy bộ câu hỏi ngẫu nhiên
export const getRandomQuestionSet = (): QuestionSet => {
  const randomIndex = Math.floor(Math.random() * questionSets.length);
  return questionSets[randomIndex];
};

// Hàm lấy câu hỏi theo cấp độ
export const getQuestionsByLevel = (setId: string, level: number): ALTPQuestion | undefined => {
  const set = getQuestionSetById(setId);
  if (!set) return undefined;
  
  return set.questions.find(q => q.id === level);
};

// Hàm lấy câu hỏi ngẫu nhiên theo cấp độ
export const getRandomQuestionByLevel = (level: number): ALTPQuestion | undefined => {
  const randomSet = getRandomQuestionSet();
  return randomSet.questions.find(q => q.id === level);
};

// Hàm định dạng tiền thưởng
export const formatMoney = (amount: number): string => {
  return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + " Sakupi";
};

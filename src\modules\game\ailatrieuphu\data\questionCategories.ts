// <PERSON><PERSON><PERSON> danh mục câu hỏi cho game Ai Là Triệu Phú
// <PERSON><PERSON> thể mở rộng thêm nhiều danh mục khác nhau

export interface QuestionCategory {
  id: string;
  name: string;
  description: string;
}

// Danh sách các danh mục câu hỏi
export const categories: QuestionCategory[] = [
  {
    id: 'general',
    name: '<PERSON>ến thức chung',
    description: '<PERSON><PERSON><PERSON> câu hỏi về kiến thức chung, đời sống, xã hội'
  },
  {
    id: 'history',
    name: '<PERSON><PERSON><PERSON> sử',
    description: 'Các câu hỏi về lịch sử Việt Nam và thế giới'
  },
  {
    id: 'geography',
    name: 'Đ<PERSON><PERSON> lý',
    description: '<PERSON><PERSON>c câu hỏi về địa lý Việt Nam và thế giới'
  },
  {
    id: 'science',
    name: 'Khoa học',
    description: 'Các câu hỏi về khoa học, v<PERSON><PERSON> lý, hó<PERSON> học, sinh học'
  },
  {
    id: 'art',
    name: '<PERSON><PERSON><PERSON> thuật',
    description: '<PERSON><PERSON><PERSON> câu hỏi về âm nhạc, hộ<PERSON> họa, đi<PERSON><PERSON> ảnh, văn học'
  },
  {
    id: 'sports',
    name: 'Thể thao',
    description: 'Các câu hỏi về thể thao Việt Nam và thế giới'
  }
];

// Các mức độ khó của câu hỏi
export enum DifficultyLevel {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
  VERY_HARD = 'very_hard'
}

// Mapping giữa số thứ tự câu hỏi và mức độ khó
export const questionDifficultyMap = {
  1: DifficultyLevel.EASY,
  2: DifficultyLevel.EASY,
  3: DifficultyLevel.EASY,
  4: DifficultyLevel.EASY,
  5: DifficultyLevel.EASY,
  6: DifficultyLevel.MEDIUM,
  7: DifficultyLevel.MEDIUM,
  8: DifficultyLevel.MEDIUM,
  9: DifficultyLevel.MEDIUM,
  10: DifficultyLevel.MEDIUM,
  11: DifficultyLevel.HARD,
  12: DifficultyLevel.HARD,
  13: DifficultyLevel.HARD,
  14: DifficultyLevel.HARD,
  15: DifficultyLevel.VERY_HARD
};

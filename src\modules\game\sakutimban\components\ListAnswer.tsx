import React, {useCallback, useMemo, useRef, useEffect, useState} from 'react';

import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Vibration,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  runOnJS,
  withTiming,
} from 'react-native-reanimated';
// import { Audio } from "expo-av";
import {useSelector, useDispatch} from 'react-redux';
import {Item} from '../../../../redux/reducers/game/sakuTBReducer';
import {useSakuTBHook} from '../../../../redux/hook/game/sakuTBHook';
import {useGameHook} from '../../../../redux/hook/gameHook';
import {nextQuestion} from '../../../../redux/reducers/game/sakuTBReducer';

const maleBirdImages = [
  require('../assets/male_bird_1.png'),
  require('../assets/male_bird_2.png'),
  require('../assets/male_bird_3.png'),
];

const femaleBirdImages = [
  require('../assets/female_bird_1.png'),
  require('../assets/female_bird_2.png'),
  require('../assets/female_bird_3.png'),
];

const birdsImages = [
  require('../assets/birds_1.png'),
  require('../assets/birds_2.png'),
  require('../assets/birds_3.png'),
];

export const playSound = async (_url: string) => {
  // await Audio.setAudioModeAsync({ playsInSilentModeIOS: true });
  // const soundObject = await Audio.Sound.createAsync(
  //   { uri: url },
  //   { shouldPlay: true }
  // );
  // return soundObject.sound;
};

// Tách AnimatedItem ra ngoài để tránh re-create component
const AnimatedItem = React.memo(
  ({
    item,
    onSelected,
    side,
    matchedPairs,
    errorPairs,
    selectedLeft,
    selectedRight,
  }: {
    item: Item;
    onSelected: (item: Item) => void;
    side: string;
    matchedPairs: string[];
    errorPairs: string[];
    selectedLeft: Item | null;
    selectedRight: Item | null;
  }) => {
    const opacity = useSharedValue(1);
    const height = useSharedValue(65);

    const animatedStyle = useAnimatedStyle(() => ({
      opacity: opacity.value,
      height: height.value,
      marginVertical: 12,
    }));

    const isMatched = useMemo(
      () => matchedPairs.includes(item.id),
      [matchedPairs, item.id],
    );
    const isError = useMemo(
      () => errorPairs.includes(item.id),
      [errorPairs, item.id],
    );
    const isSelected = useMemo(() => {
      if (side === 'left') return selectedLeft?.id === item.id;
      return selectedRight?.id === item.id;
    }, [side, selectedLeft, selectedRight, item.id]);

    const handlePress = useCallback(() => {
      onSelected(item);
    }, [onSelected, item]);

    const getItem = useMemo(() => {
      if (item.type === 'image') {
        return <Image source={{uri: item.text}} style={styles.itemImage} />;
      } else if (item.type === 'audio') {
        return <Image source={require('../assets/audio.png')} />;
      } else {
        return (
          <Text key={item.id} style={styles.textAnswer}>
            {item.text}
          </Text>
        );
      }
    }, [item]);

    return (
      <Animated.View style={[animatedStyle]}>
        <View>
          <TouchableOpacity
            style={[
              styles.answer,
              isSelected && styles.selectedItem,
              isMatched && styles.matchedItem,
              isError && styles.errorItem,
            ]}
            onPress={handlePress}
            activeOpacity={0.7}>
            {getItem}
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  },
);

const ListAnswer = () => {
  const dispatch = useDispatch();
  const {currentLives} = useSelector((state: any) => state.gameStore);
  const sakuTbHook = useSakuTBHook();
  const gameHook = useGameHook();
  const {
    listItemsLeft,
    listItemsRight,
    selectedLeft,
    selectedRight,
    matchedPairs,
    errorPairs,
    questionDone,
    listItemsDone,
    currentQuestionIndex,
    questions,
    totalQuestion,
  } = useSelector((state: any) => state.SakuTB);

  const [imageIndex, setImageIndex] = useState(0);

  // Debounce ref để tránh multiple clicks
  const isProcessing = useRef(false);
  const questionCompleted = useRef(false);
  const nextQuestionTimeout = useRef<NodeJS.Timeout | null>(null);

  // Reset questionCompleted khi chuyển câu hỏi
  useEffect(() => {
    questionCompleted.current = false;
    // Clear any pending timeout
    if (nextQuestionTimeout.current) {
      clearTimeout(nextQuestionTimeout.current);
      nextQuestionTimeout.current = null;
    }
    console.log(
      `[ListAnswer] 🔄 Reset for question ${currentQuestionIndex + 1}`,
    );

    const numberRandom = Math.floor(Math.random() * 3); // 0, 1, 2
    setImageIndex(numberRandom);
  }, [currentQuestionIndex]);

  // Loại bỏ useEffect auto-check để tránh conflict

  // Xoá item ở list bên trái - Tối ưu với useCallback
  const deleteItemLeft = useCallback(
    (id: string) => {
      sakuTbHook.setData(
        'listItemsLeft',
        listItemsLeft.filter((item: {id: string}) => item.id !== id),
      );
    },
    [listItemsLeft, sakuTbHook],
  );

  // Xoá item ở list bên phải - Tối ưu với useCallback
  const deleteItemRight = useCallback(
    (id: string) => {
      sakuTbHook.setData(
        'listItemsRight',
        listItemsRight.filter((item: {id: string}) => item.id !== id),
      );
    },
    [listItemsRight, sakuTbHook],
  );

  // Kiểm tra đáp án có match hay không - Tối ưu với useCallback
  const checkMatch = useCallback(
    (leftItem: Item, rightItem: Item) => {
      if (leftItem.matchId === rightItem.matchId) {
        // Đúng - Batch multiple updates
        sakuTbHook.setData('matchedPairs', [
          ...matchedPairs,
          leftItem.id,
          rightItem.id,
        ]);

        // Sử dụng setTimeout để tối ưu animation
        setTimeout(() => {
          deleteItemLeft(leftItem.id);
          deleteItemRight(rightItem.id);
          sakuTbHook.setData('listItemsDone', [
            ...listItemsDone,
            {listItems: [leftItem, rightItem]},
          ]);

          // Kiểm tra xem có phải cặp cuối cùng không
          const newMatchedPairs = [...matchedPairs, leftItem.id, rightItem.id];
          const newLeftItems = listItemsLeft.filter(
            (item: Item) => item.id !== leftItem.id,
          );
          const newRightItems = listItemsRight.filter(
            (item: Item) => item.id !== rightItem.id,
          );

          console.log(
            `[checkMatch] After match - newMatchedPairs: ${newMatchedPairs.length}, newLeftItems: ${newLeftItems.length}, newRightItems: ${newRightItems.length}`,
          );

          // Nếu đây là cặp cuối cùng của câu hỏi hiện tại
          if (
            newMatchedPairs.length === 10 &&
            newLeftItems.length === 0 &&
            newRightItems.length === 0 &&
            !questionCompleted.current
          ) {
            console.log(
              `[checkMatch] ✅ Question ${currentQuestionIndex + 1}/${
                questions.length
              } completed!`,
            );
            console.log(
              `[checkMatch] Current state - questionDone: ${questionDone}, totalQuestion: ${totalQuestion}`,
            );

            questionCompleted.current = true; // Đánh dấu đã hoàn thành để tránh trigger lại

            // Clear any existing timeout
            if (nextQuestionTimeout.current) {
              clearTimeout(nextQuestionTimeout.current);
            }

            nextQuestionTimeout.current = setTimeout(() => {
              if (currentQuestionIndex < questions.length - 1) {
                console.log(
                  `[checkMatch] 🔄 Moving from question ${
                    currentQuestionIndex + 1
                  } to ${currentQuestionIndex + 2}`,
                );
                dispatch(nextQuestion());
                sakuTbHook.setData('questionDone', questionDone + 1);
                console.log(
                  `[checkMatch] ✅ Dispatched nextQuestion and updated questionDone to ${
                    questionDone + 1
                  }`,
                );
              } else {
                console.log('[checkMatch] 🎉 All questions completed!');
                sakuTbHook.setData('questionDone', totalQuestion);
              }
              nextQuestionTimeout.current = null;
            }, 1000);
          } else {
            console.log(
              `[checkMatch] ❌ Not completed yet - newMatchedPairs: ${newMatchedPairs.length}/10, newLeftItems: ${newLeftItems.length}, newRightItems: ${newRightItems.length}, questionCompleted: ${questionCompleted.current}`,
            );
          }

          isProcessing.current = false;
        }, 300); // Giảm delay từ 500ms xuống 300ms
      } else {
        // Sai - Rung thiết bị
        // Vibration.vibrate([0, 500, 200, 500]);

        sakuTbHook.setData('errorPairs', [
          ...errorPairs,
          leftItem.id,
          rightItem.id,
        ]);
        gameHook.setData({
          stateName: 'currentLives',
          value: currentLives - 1,
        });
      }

      setTimeout(() => {
        sakuTbHook.setData('selectedLeft', null);
        sakuTbHook.setData('selectedRight', null);
        sakuTbHook.setData('errorPairs', []);
        if (leftItem.matchId !== rightItem.matchId) {
          isProcessing.current = false;
        }
      }, 300); // Giảm delay
    },
    [
      matchedPairs,
      errorPairs,
      listItemsDone,
      currentLives,
      sakuTbHook,
      gameHook,
      deleteItemLeft,
      deleteItemRight,
    ],
  );

  // Chọn đáp án bên trái
  const handleLeftSelect = useCallback(
    (item: Item) => {
      if (isProcessing.current || matchedPairs.includes(item.id)) {
        return;
      }

      isProcessing.current = true;
      sakuTbHook.setData('selectedLeft', item);

      if (selectedRight) {
        checkMatch(item, selectedRight);
      } else {
        isProcessing.current = false;
      }
    },
    [matchedPairs, selectedRight, sakuTbHook, checkMatch],
  );

  // Chọn đáp án bên phải
  const handleRightSelect = useCallback(
    (item: Item) => {
      if (isProcessing.current || matchedPairs.includes(item.id)) {
        return;
      }

      isProcessing.current = true;
      if (item.type === 'audio') {
        playSound(item.text);
      }

      sakuTbHook.setData('selectedRight', item);

      if (selectedLeft) {
        checkMatch(selectedLeft, item);
      } else {
        isProcessing.current = false;
      }
    },
    [matchedPairs, selectedLeft, sakuTbHook, checkMatch],
  );

  // Hiển thị item đã đúng
  const getItemDone = (item: Item) => {
    if (item.type === 'image') {
      return (
        <Image
          style={{width: 65, height: 65}}
          source={{uri: item.text}}></Image>
      );
    } else if (item.type === 'audio') {
      return <Image source={require('../assets/audio.png')}></Image>;
    } else {
      return (
        <Text key={item.id} style={styles.textAnswer}>
          {item.text}
        </Text>
      );
    }
  };

  return (
    <View>
      <View
        style={{
          width: '100%',
          flexDirection: 'row',
          justifyContent: 'space-around',
        }}>
        <View style={styles.container}>
          <Image
            style={{height: 80}}
            source={femaleBirdImages[imageIndex]}></Image>
          <ScrollView>
            {listItemsLeft.map((item: Item) => (
              <AnimatedItem
                item={item}
                key={item.id}
                onSelected={handleLeftSelect}
                side="left"
                matchedPairs={matchedPairs}
                errorPairs={errorPairs}
                selectedLeft={selectedLeft}
                selectedRight={selectedRight}
              />
            ))}
          </ScrollView>
        </View>
        <View style={styles.container}>
          <Image
            style={{height: 80}}
            source={maleBirdImages[imageIndex]}></Image>
          <ScrollView>
            {listItemsRight.map((item: Item) => (
              <AnimatedItem
                item={item}
                key={item.id}
                onSelected={handleRightSelect}
                side="right"
                matchedPairs={matchedPairs}
                errorPairs={errorPairs}
                selectedLeft={selectedLeft}
                selectedRight={selectedRight}
              />
            ))}
          </ScrollView>
        </View>
      </View>
      <View>
        {listItemsDone.map((item: any, index: number) => (
          <View style={styles.doneItem} key={index}>
            <View style={{width: 70}}>
              <Image source={birdsImages[imageIndex]}></Image>
            </View>
            {item.listItems.map((item: Item) => (
              <View key={item.id} style={{flex: 1, marginLeft: 12}}>
                {getItemDone(item)}
              </View>
            ))}
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '45%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  answer: {
    width: 140,
    height: 65,
    backgroundColor: '#FCF8E8',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
  },
  textAnswer: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
  },
  itemImage: {
    width: 50,
    height: 50,
  },
  selectedItem: {
    backgroundColor: '#2196F3',
    borderColor: '#0d8aee',
  },
  matchedItem: {
    backgroundColor: '#4CAF50',
    borderColor: '#3d8b40',
  },
  errorItem: {
    backgroundColor: '#FF5722',
    borderColor: '#3d8b40',
  },
  doneItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 12,
  },
});

export default ListAnswer;

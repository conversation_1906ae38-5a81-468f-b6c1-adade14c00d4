import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {DataController} from '../../../base/baseController';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {groupMemberRoleStatus, StorageContanst} from '../../../Config/Contanst';

interface GroupWithMemberCount {
  Id: string;
  Name: string;
  Thumb: string;
  Description: string;
  MemberCount: number;
  CustomerId: string;
}
interface confirmGroupstate {
  groups: GroupWithMemberCount[];
  isLoading: boolean;
  error: string | null;
  page: number;
  hasMore: boolean;
}

const initialState: confirmGroupstate = {
  groups: [],
  isLoading: false,
  error: null,
  page: 1,
  hasMore: true,
};

export const confirmGroupsSlice = createSlice({
  name: 'confirmGroups',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setGroups: (
      state,
      action: PayloadAction<{groups: GroupWithMemberCount[]}>,
    ) => {
      state.groups = action.payload.groups;
      // state.page = action.payload.page;
      // state.hasMore = action.payload.groups.length > 0;
    },
    appendGroups: (
      state,
      action: PayloadAction<{groups: GroupWithMemberCount[]; page: number}>,
    ) => {
      state.groups = [...state.groups, ...action.payload.groups];
      state.page = action.payload.page;
      state.hasMore = action.payload.groups.length > 0;
    },
    updateMemberCount: (
      state,
      action: PayloadAction<{groupId: string; change: number}>,
    ) => {
      state.groups = state.groups.map(group =>
        group.Id === action.payload.groupId
          ? {...group, MemberCount: group.MemberCount + action.payload.change}
          : group,
      );
    },
    removeGroup: (state, action: PayloadAction<string>) => {
      state.groups = state.groups.filter(group => group.Id !== action.payload);
    },
    resetState: () => initialState,
    addFollowingGroup: (state, action: PayloadAction<GroupWithMemberCount>) => {
      state.groups = [action.payload, ...state.groups];
    },
    removeFollowingGroup: (state, action: PayloadAction<string>) => {
      state.groups = state.groups.filter(group => group.Id !== action.payload);
    },
  },
});
export default confirmGroupsSlice.reducer;

const {
  setLoading,
  setGroups,
  removeFollowingGroup,
} = confirmGroupsSlice.actions;
export class confirmGroupsActions {
  static getconfirmGroups = () => async (dispatch: Dispatch) => {
    dispatch(setLoading(true));
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);

    if (cusId) {
      const groupMemberController = new DataController('Role');
      const result = await groupMemberController.getListSimple({
        query: `@CustomerId:{${cusId}}  @Status:[${groupMemberRoleStatus.invited}]`,
        page: 1,
        size: 20,
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        returns: ['GroupId'],
      });
      if (result.code === 200) {
        const groupIds = result.data.map((member: any) => member.GroupId);

        if (groupIds.length > 0) {
          const groupController = new DataController('Group');
          const groupsResult = await groupController.getListSimple({
            query: `@Id:{${groupIds.join(' | ')}}`,
            returns: ['Id', 'Name', 'Thumb', 'Description', 'CustomerId'],
          });
          if (groupsResult.code === 200) {
            dispatch(setGroups({groups: groupsResult.data}));
          } else {
            dispatch(setGroups({groups: []}));
          }
          // dispatch(setGroups({groups: groupsResult}));
        } else {
          dispatch(setGroups({groups: []}));
        }
      }
    }
    dispatch(setLoading(false));
  };

  static AcceptGroup = (groupId: string) => async (dispatch: Dispatch) => {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const groupMemberController = new DataController('Role');
    const response = await groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${cusId}} @Status:[${groupMemberRoleStatus.invited}]`,
      size: 1,
      returns: ['Id'],
    });
    if (response?.code === 200 && response.data?.length > 0) {
      const data = response.data[0];
      data.Status = groupMemberRoleStatus.joined; // xác nhận
      const result = await groupMemberController.edit([data]);
      if (result?.code === 200) {
        dispatch(removeFollowingGroup(groupId));
      }
    }
  };
  static rejectGroup = (groupId: string) => async (dispatch: Dispatch) => {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const groupMemberController = new DataController('Role');
    const response = await groupMemberController.getListSimple({
      query: `@GroupId:{${groupId}} @CustomerId:{${cusId}} @Status:[${groupMemberRoleStatus.invited}]`,
      size: 1,
      returns: ['Id'],
    });
    if (response?.code === 200 && response.data?.length > 0) {
      const data = response.data[0];
      const result = await groupMemberController.delete([data.Id]);
      if (result?.code === 200) {
        dispatch(removeFollowingGroup(groupId));
      }
    }
  };
  static removeGroups = (groupId: string) => (dispatch: Dispatch) => {
    dispatch(removeFollowingGroup(groupId));
  };
}

import React from 'react';
import {View, TouchableOpacity, StyleSheet, Image} from 'react-native';

interface LifelineButtonsProps {
  availableLifelines: {
    fiftyFifty: boolean;
    phoneCall: boolean;
    audience: boolean;
    expert: boolean;
  };
  useLifeline: (lifeline: string) => void;
  isEnabled: boolean;
}

const LifelineButtons: React.FC<LifelineButtonsProps> = ({
  availableLifelines,
  useLifeline,
  isEnabled,
}) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.lifelineButton,
          !availableLifelines.fiftyFifty && styles.usedLifeline,
        ]}
        onPress={() =>
          availableLifelines.fiftyFifty &&
          isEnabled &&
          useLifeline('fiftyFifty')
        }
        disabled={!availableLifelines.fiftyFifty || !isEnabled}>
        <Image
          source={require('../assets/50_50.png')}
          style={styles.lifelineIcon}
          resizeMode="contain"
        />
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.lifelineButton,
          !availableLifelines.phoneCall && styles.usedLifeline,
        ]}
        onPress={() =>
          availableLifelines.phoneCall && isEnabled && useLifeline('phoneCall')
        }
        disabled={!availableLifelines.phoneCall || !isEnabled}>
        <Image
          source={require('../assets/phone-call.png')}
          style={styles.lifelineIcon}
          resizeMode="contain"
        />
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.lifelineButton,
          !availableLifelines.audience && styles.usedLifeline,
        ]}
        onPress={() =>
          availableLifelines.audience && isEnabled && useLifeline('audience')
        }
        disabled={!availableLifelines.audience || !isEnabled}>
        <Image
          source={require('../assets/khangia.png')}
          style={styles.lifelineIcon}
          resizeMode="contain"
        />
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.lifelineButton,
          !availableLifelines.expert && styles.usedLifeline,
        ]}
        onPress={() =>
          availableLifelines.expert && isEnabled && useLifeline('expert')
        }
        disabled={!availableLifelines.expert || !isEnabled}>
        <Image
          source={require('../assets/chuyengia.png')}
          style={styles.lifelineIcon}
          resizeMode="contain"
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  lifelineButton: {
    backgroundColor: 'rgba(30, 58, 138, 0.8)',
    padding: 10,
    borderRadius: 50,
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFD700',
  },
  usedLifeline: {
    opacity: 0.3,
    borderColor: '#888',
  },
  lifelineIcon: {
    width: 30,
    height: 30,
    tintColor: 'white',
  },
});

export default LifelineButtons;

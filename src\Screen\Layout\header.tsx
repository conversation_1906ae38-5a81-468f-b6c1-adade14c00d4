import {
  GestureResponderEvent,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {Text} from 'react-native-paper';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';
import {Winicon} from 'wini-mobile-components';
import {useTranslation} from 'react-i18next';

export default function ScreenHeader({
  onBack,
  title,
  action,
  height,
  bottom,
  children,
  backIcon,
  style = {},
  titleStyle = {},
  prefix,
}: {
  onBack?: ((event: GestureResponderEvent) => void) & (() => void);
  title?: React.ReactNode | string;
  action?: React.ReactNode;
  bottom?: React.ReactNode;
  height?: number;
  children?: React.ReactNode;
  backIcon?: React.ReactNode;
  prefix?: React.ReactNode;
  style?: ViewStyle;
  titleStyle?: TextStyle;
}) {
  const {t} = useTranslation();
  return (
    <View style={[styles.container, style]}>
      {children ?? (
        <View
          style={{
            ...styles.header,
            height: height ?? style.height ?? 56,
          }}>
          {prefix}
          {onBack ? (
            <TouchableOpacity
              style={{
                paddingLeft: 16,
                paddingRight: 16,
                paddingVertical: 8,
                alignItems: 'center',
              }}
              onPress={onBack}>
              {backIcon ?? (
                <View
                  style={{
                    gap: 4,
                    flexDirection: 'row',
                    width: '100%',
                    alignItems: 'center',
                  }}>
                  <Winicon src="outline/arrows/left-arrow" size={20} />
                  {/* <Text
                    style={{
                      ...TypoSkin.heading8,
                      color: ColorThemes.light.Neutral_Text_Color_Title,
                    }}>
                    {t('common.back')}
                  </Text> */}
                </View>
              )}
            </TouchableOpacity>
          ) : undefined}
          <View style={styles.title}>
            {typeof title === 'string' ? (
              <Text
                numberOfLines={2}
                style={[
                  TypoSkin.title3,
                  {
                    textAlign: 'center',
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                    ...titleStyle,
                  },
                ]}>
                {title ?? '-'}
              </Text>
            ) : (
              title ?? <View />
            )}
          </View>
          {action}
        </View>
      )}
      {bottom}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: ColorThemes.light.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    alignSelf: 'center',
  },
  title: {
    position: 'absolute',
    left: '12%',
    right: '12%',

    paddingBottom: 8,
  },
});

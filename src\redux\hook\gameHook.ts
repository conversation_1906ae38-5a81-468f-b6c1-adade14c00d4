import {useDispatch} from 'react-redux';
import {setData, gameOver, restartGame, reset} from '../reducers/gameReducer';
import {getCurrentScore} from '../../modules/game/gameAsyncThunk';
import {CustomerActions} from '../reducers/CustomerReducer';
import { GameDA } from '../../modules/game/gameDA';
import store from '../store/store';
import { GameStatus } from '../../Config/Contanst';

const gameDA = new GameDA();

export const useGameHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    pauseGame: () => {
      dispatch(setData({stateName: 'isRunTime', value: false}));
    },
    continueGame: () => {
      dispatch(setData({stateName: 'isRunTime', value: true}));
    },
    resetGame: () => {
      dispatch(reset());
    },
    gameOver: (message: string) => {
      dispatch(gameOver(message));
    },
    restartGame: () => {
      dispatch(restartGame());
    },
    getCurrentScore: async (gameId: string) => {
      return dispatch(getCurrentScore(gameId) as any);
    },
    updateScore: async ({
      totalScore,
      gameId,
      name,
      competenceId,
      status,
      currentMilestoneId,
    }: {
      name: string;
      totalScore: number;
      gameId: string;
      competenceId: string;
      currentMilestoneId: number;
      status: number;
    }) => {
      await gameDA.insertScore({
        milestoneId: currentMilestoneId,
        gameId: gameId,
        competenceId: competenceId,
        status,
        score: totalScore,
        name,
      });
      dispatch(getCurrentScore(gameId) as any);
    },
  };

  return action;
};

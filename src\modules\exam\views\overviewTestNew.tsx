import {ScrollView, Text, View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import {navigateBack, RootScreen} from '../../../router/router';
import {AppButton, HashTag, ListTile, Winicon} from 'wini-mobile-components';
import WScreenFooter from '../../../Screen/Layout/footer';
import {TypoSkin} from '../../../assets/skin/typography';
import {useNavigation, useRoute} from '@react-navigation/native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StatusExam} from '../../../Config/Contanst';
import {useTranslation} from 'react-i18next';

export default function OverviewTestNew() {
  const {t} = useTranslation();
  const route = useRoute<any>();
  const {id, type, Step, totalQuestions, timeLimit, Score, name, section} =
    route.params;
  const navigation = useNavigation<any>();
  // const {examInfor, listQuestion, loading} = useExamData(id);

  function CheckResult({item}: any) {
    const isPass = item?.Status == StatusExam.passed;
    return (
      <Text
        style={{
          ...TypoSkin.title3,
          color: isPass
            ? ColorThemes.light.success_main_color
            : ColorThemes.light.error_main_color,
        }}>
        {isPass ? t('exam.pass') : t('exam.fail')}
      </Text>
    );
  }
  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <ScreenHeader
        title={`${name ?? ''}`}
        backIcon={<Winicon src="outline/arrows/left-arrow" size={20} />}
        onBack={() => {
          navigateBack();
        }}
      />
      {/* content */}
      <ScrollView style={{flex: 1}}>
        <View style={{gap: 24}}>
          <ListTile
            style={{paddingHorizontal: 16, padding: 0}}
            leading={<Winicon src="fill/user interface/c-question" size={20} />}
            title={t('exam.totalQuestions', {count: totalQuestions})}
            bottom={
              section?.length > 0 ? (
                <View
                  style={{
                    flex: 1,
                    width: '100%',
                    paddingTop: 16,
                    flexWrap: 'wrap',
                    flexDirection: 'row',
                  }}>
                  {section.map((item: any, index: number) => {
                    return (
                      <HashTag
                        key={index}
                        // title={`${item.Name} (${item.Time ?? 0} phút)`}
                        title={`${item.Name}`}
                      />
                    );
                  })}
                </View>
              ) : null
            }
          />
          <ListTile
            style={{paddingHorizontal: 16, padding: 0}}
            leading={<Winicon src="fill/user interface/clock" size={20} />}
            title={t('exam.timeLimit', {minutes: timeLimit})}
          />
          <ListTile
            style={{paddingHorizontal: 16, padding: 0}}
            leading={<Winicon src="fill/user interface/c-warning" size={20} />}
            title={t('exam.requirements')}
            bottom={
              <View style={{flex: 1, width: '100%', paddingTop: 16}}>
                <Text style={{...TypoSkin.body3}}>
                  {t('exam.passingRequirement', {
                    percent: Score ?? 0,
                  })}
                </Text>
              </View>
            }
          />
          {/* <ListTile
            style={{paddingHorizontal: 16, padding: 0}}
            leading={
              <Winicon src="fill/user interface/warning-sign" size={20} />
            }
            title={
              !examInfor?.Count
                ? `${t('exam.results', {
                    count: examInfor?.TestResults?.length ?? 0,
                  })}`
                : `${examInfor?.CountTest ?? 0}/${examInfor?.Count ?? 0}`
            }
            bottom={
              loading ? (
                <View
                  style={{
                    flex: 1,
                    width: '100%',
                    paddingTop: 16,
                    paddingBottom: 100,
                  }}>
                  <Text style={{...TypoSkin.body3}}>...</Text>
                </View>
              ) : (
                <View
                  style={{
                    flex: 1,
                    width: '100%',
                    paddingTop: 16,
                    paddingBottom: 100,
                  }}>
                  {examInfor?.TestResults?.length ? (
                    examInfor?.TestResults?.map((item: any, index: number) => {
                      return (
                        <ListTile
                          key={index}
                          onPress={
                            examInfor.Type === ExamType.Real
                              ? undefined
                              : () => {
                                  navigate(RootScreen.tryingHistoryTest, {
                                    id: item.Id,
                                  });
                                }
                          }
                          style={{padding: 0, paddingVertical: 8}}
                          title={
                            <View
                              style={{
                                width: '100%',
                                gap: 8,
                                flexDirection: 'row',
                                alignItems: 'center',
                              }}>
                              <Text
                                style={{
                                  ...TypoSkin.heading7,
                                  color:
                                    ColorThemes.light.neutral_text_title_color,
                                }}>
                                {t('exam.attempt', {number: index + 1})}:
                              </Text>
                              <CheckResult item={item} />
                            </View>
                          }
                          subtitle={
                            <View
                              style={{
                                width: '100%',
                                paddingTop: 4,
                                alignContent: 'flex-start',
                              }}>
                              <Text
                                style={{
                                  ...TypoSkin.subtitle3,
                                  color:
                                    ColorThemes.light
                                      .neutral_text_subtitle_color,
                                }}>
                                {`${Ultis.datetoString(
                                  new Date(item.DateCreated ?? 0),
                                  'dd/MM/yyyy hh:mm',
                                )}`}
                              </Text>
                            </View>
                          }
                          trailing={
                            examInfor.Type === ExamType.Real ? null : (
                              <View
                                style={{
                                  width: '100%',
                                }}>
                                <Winicon
                                  src="outline/user interface/view"
                                  color={
                                    ColorThemes.light
                                      .neutral_text_subtitle_color
                                  }
                                  size={16}
                                />
                              </View>
                            )
                          }
                        />
                      );
                    })
                  ) : (
                    <Text style={{...TypoSkin.body3}}>
                      {t('exam.noResults')}
                    </Text>
                  )}
                </View>
              )
            }
          /> */}
        </View>
      </ScrollView>
      {/*  */}
      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 32,
        }}>
        <AppButton
          title={t('exam.startExam')}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
          borderColor="transparent"
          // disabled={(examInfor?.CountTest ?? 0) >= (examInfor?.Count ?? 0)}
          containerStyle={{
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={() => {
            navigation.push(RootScreen.DoingTestNewRedesigned, {
              id: id,
              type: type,
              Step: Step,
              testId: id,
            });
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
}

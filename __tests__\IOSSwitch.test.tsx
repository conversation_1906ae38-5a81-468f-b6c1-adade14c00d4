import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import IOSSwitch from '../src/components/IOSSwitch';

describe('IOSSwitch', () => {
  it('renders correctly with default props', () => {
    const onValueChange = jest.fn();
    const { getByTestId } = render(
      <IOSSwitch 
        value={false} 
        onValueChange={onValueChange} 
        testID="ios-switch"
      />
    );
    
    expect(getByTestId('ios-switch')).toBeTruthy();
  });
  
  it('calls onValueChange when pressed', () => {
    const onValueChange = jest.fn();
    const { getByTestId } = render(
      <IOSSwitch 
        value={false} 
        onValueChange={onValueChange} 
        testID="ios-switch"
      />
    );
    
    fireEvent.press(getByTestId('ios-switch'));
    expect(onValueChange).toHaveBeenCalledWith(true);
  });
  
  it('does not call onValueChange when disabled', () => {
    const onValueChange = jest.fn();
    const { getByTestId } = render(
      <IOSSwitch 
        value={false} 
        onValueChange={onValueChange} 
        disabled={true}
        testID="ios-switch"
      />
    );
    
    fireEvent.press(getByTestId('ios-switch'));
    expect(onValueChange).not.toHaveBeenCalled();
  });
});

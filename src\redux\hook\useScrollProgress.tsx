import {useEffect, useRef} from 'react';
import {NativeSyntheticEvent, NativeScrollEvent} from 'react-native';
import {useDispatch} from 'react-redux';
import {LessonActions} from '../reducers/proccessLessonReducer';
import {AppDispatch} from '../store/store';

interface ScrollProgressOptions {
  step: any;
  contentHeight: number;
  scrollViewHeight: number;
  isActive: boolean;
}

export function useScrollProgress({
  step,
  contentHeight,
  scrollViewHeight,
  isActive,
}: ScrollProgressOptions) {
  const dispatch: AppDispatch = useDispatch();
  const lastReportedProgress = useRef(0);
  const hasDispatchedFull = useRef(false);

  // 🔥 Gửi 100% nếu content không scroll được
  useEffect(() => {
    if (
      isActive &&
      contentHeight > 0 &&
      scrollViewHeight > 0 &&
      contentHeight <= scrollViewHeight &&
      !hasDispatchedFull.current
    ) {
      hasDispatchedFull.current = true;
      lastReportedProgress.current = 100;
      console.log('Auto 100% progress (no scroll needed)');
    }
  }, [contentHeight, scrollViewHeight, dispatch, step, isActive]);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const {contentOffset, contentSize, layoutMeasurement} = event.nativeEvent;

    const scrollY = contentOffset.y;
    const totalHeight = contentSize.height;
    const visibleHeight = layoutMeasurement.height;

    const scrollableHeight = totalHeight - visibleHeight;
    if (scrollableHeight <= 0 || !isActive) {
      return;
    }

    const scrollPercent = (scrollY / scrollableHeight) * 100;
    const progressLevel = getProgressLevel(scrollPercent);

    if (progressLevel > lastReportedProgress.current) {
      lastReportedProgress.current = progressLevel;
      console.log(`Scroll progress: ${progressLevel}%`);
    }
  };

  return handleScroll;
}

function getProgressLevel(percent: number): number {
  if (percent >= 100) {
    return 100;
  }
  if (percent >= 75) {
    return 75;
  }
  if (percent >= 50) {
    return 50;
  }
  if (percent >= 25) {
    return 25;
  }
  return 0;
}

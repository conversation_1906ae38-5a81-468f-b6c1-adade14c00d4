import { useState, useEffect } from 'react';
import { Dimensions } from 'react-native';
import { 
  responsivePositions, 
  getCurrentBreakpoint, 
  aspectRatioPositions,
  gridPositions 
} from '../data/pathPositions';

export type PositionStrategy = 'responsive' | 'aspectRatio' | 'grid' | 'percentage';

interface MilestonePosition {
  id: number;
  top: number;
  left: number;
  x?: number;
  y?: number;
}

export const useResponsivePositions = (
  strategy: PositionStrategy = 'responsive',
  containerDimensions?: { width: number; height: number }
) => {
  const [positions, setPositions] = useState<MilestonePosition[]>([]);
  const [screenDimensions, setScreenDimensions] = useState(Dimensions.get('window'));

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenDimensions(window);
    });

    return () => subscription?.remove();
  }, []);

  useEffect(() => {
    const calculatePositions = () => {
      const containerWidth = containerDimensions?.width || screenDimensions.width;
      const containerHeight = containerDimensions?.height || screenDimensions.height;

      switch (strategy) {
        case 'responsive':
          return calculateResponsivePositions();
          
        case 'aspectRatio':
          return calculateAspectRatioPositions(containerWidth, containerHeight);
          
        case 'grid':
          return calculateGridPositions(containerWidth, containerHeight);
          
        case 'percentage':
        default:
          return calculatePercentagePositions();
      }
    };

    setPositions(calculatePositions());
  }, [strategy, screenDimensions, containerDimensions]);

  // Giải pháp 1: Responsive breakpoints
  const calculateResponsivePositions = (): MilestonePosition[] => {
    const breakpoint = getCurrentBreakpoint();
    return responsivePositions.positions[breakpoint] || responsivePositions.positions.medium;
  };

  // Giải pháp 2: Aspect ratio based
  const calculateAspectRatioPositions = (width: number, height: number): MilestonePosition[] => {
    return aspectRatioPositions.calculatePositions(width, height);
  };

  // Giải pháp 3: Grid based
  const calculateGridPositions = (width: number, height: number): MilestonePosition[] => {
    return gridPositions.milestoneGridPositions.map(milestone => {
      const gridPos = gridPositions.calculateGridPosition(
        milestone.row, 
        milestone.col, 
        width, 
        height
      );
      
      return {
        id: milestone.id,
        top: gridPos.y / height,  // Convert to percentage
        left: gridPos.x / width,  // Convert to percentage
        x: gridPos.x,
        y: gridPos.y,
      };
    });
  };

  // Giải pháp 4: Enhanced percentage với safe areas
  const calculatePercentagePositions = (): MilestonePosition[] => {
    const { width, height } = screenDimensions;
    const aspectRatio = width / height;
    
    // Base positions
    let basePositions = [
      { id: 1, top: 0.75, left: 0.15 },
      { id: 2, top: 0.6, left: 0.25 },
      { id: 3, top: 0.45, left: 0.4 },
      { id: 4, top: 0.3, left: 0.6 },
      { id: 5, top: 0.15, left: 0.75 },
      { id: 6, top: 0.1, left: 0.55 },
      { id: 7, top: 0.05, left: 0.3 },
    ];

    // Điều chỉnh cho safe areas và notch
    const safeAreaTop = 0.05; // 5% cho status bar/notch
    const safeAreaBottom = 0.1; // 10% cho home indicator
    const safeAreaSides = 0.05; // 5% cho các cạnh

    return basePositions.map(pos => ({
      ...pos,
      top: safeAreaTop + pos.top * (1 - safeAreaTop - safeAreaBottom),
      left: safeAreaSides + pos.left * (1 - 2 * safeAreaSides),
    }));
  };

  return {
    positions,
    screenDimensions,
    strategy,
  };
};

// Hook để tính toán vị trí pixel thực tế
export const usePixelPositions = (
  strategy: PositionStrategy = 'responsive',
  containerDimensions: { width: number; height: number }
) => {
  const { positions } = useResponsivePositions(strategy, containerDimensions);

  const pixelPositions = positions.map(pos => ({
    id: pos.id,
    x: pos.left * containerDimensions.width,
    y: pos.top * containerDimensions.height,
    percentageTop: pos.top,
    percentageLeft: pos.left,
  }));

  return pixelPositions;
};

// Hook để auto-detect strategy tốt nhất
export const useOptimalPositionStrategy = () => {
  const [optimalStrategy, setOptimalStrategy] = useState<PositionStrategy>('responsive');
  const screenDimensions = Dimensions.get('window');

  useEffect(() => {
    const { width, height } = screenDimensions;
    const aspectRatio = width / height;

    // Logic để chọn strategy tốt nhất
    if (width >= 768) {
      // Tablet - dùng grid
      setOptimalStrategy('grid');
    } else if (aspectRatio > 0.7) {
      // Landscape - dùng aspect ratio
      setOptimalStrategy('aspectRatio');
    } else if (aspectRatio < 0.4) {
      // Màn hình rất dài - dùng responsive
      setOptimalStrategy('responsive');
    } else {
      // Default - dùng percentage với safe area
      setOptimalStrategy('percentage');
    }
  }, [screenDimensions]);

  return optimalStrategy;
};

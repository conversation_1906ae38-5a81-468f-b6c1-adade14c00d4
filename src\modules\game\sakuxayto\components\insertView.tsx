import React from 'react';
import {useRef} from 'react';
import {View, Animated, Easing, Vibration, StyleSheet} from 'react-native';

interface InsertZoneProps {
  onReceiveDragDrop: (word: string) => void;
  index: number;
  isActive?: boolean;
  onDropSuccess?: () => void;
  isEndZone?: boolean; // Special styling for end zone
  isDraggingAnyWord?: boolean;
}

export const InsertZone = React.forwardRef<View, InsertZoneProps>(
  ({isActive, onDropSuccess, isEndZone = false, isDraggingAnyWord}, ref) => {
    const scaleAnim = useRef(new Animated.Value(1)).current;
    const opacityAnim = useRef(new Animated.Value(0.3)).current;
    const pulseAnim = useRef(new Animated.Value(1)).current;
    const successScaleAnim = useRef(new Animated.Value(1)).current;

    // Animation for when zone becomes active (word being dragged over)
    React.useEffect(() => {
      if (isActive) {
        // Start hover animations with smoother easing
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 1.3,
            duration: 150,
            easing: Easing.out(Easing.back(1.2)),
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 150,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
          // Smoother pulse animation
          Animated.loop(
            Animated.sequence([
              Animated.timing(pulseAnim, {
                toValue: 1.05,
                duration: 800,
                easing: Easing.inOut(Easing.sin),
                useNativeDriver: true,
              }),
              Animated.timing(pulseAnim, {
                toValue: 1,
                duration: 800,
                easing: Easing.inOut(Easing.sin),
                useNativeDriver: true,
              }),
            ]),
          ),
        ]).start();
      } else {
        // Reset to normal state with smooth transition
        Animated.parallel([
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 200,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
          Animated.timing(opacityAnim, {
            toValue: 0.4,
            duration: 200,
            easing: Easing.out(Easing.quad),
            useNativeDriver: true,
          }),
        ]).start();

        // Stop pulse animation smoothly
        pulseAnim.stopAnimation();
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 100,
          easing: Easing.out(Easing.quad),
          useNativeDriver: true,
        }).start();
      }
    }, [isActive, scaleAnim, opacityAnim, pulseAnim]);

    // Success animation when word is dropped
    const triggerSuccessAnimation = React.useCallback(() => {
      // Add haptic feedback
      Vibration.vibrate(50, false);

      Animated.sequence([
        // Quick scale up with bounce
        Animated.timing(successScaleAnim, {
          toValue: 1.4,
          duration: 120,
          easing: Easing.out(Easing.back(1.5)),
          useNativeDriver: true,
        }),
        // Smooth scale back with elastic feel
        Animated.timing(successScaleAnim, {
          toValue: 1,
          duration: 300,
          easing: Easing.out(Easing.elastic(1.2)),
          useNativeDriver: true,
        }),
      ]).start(() => {
        if (onDropSuccess) {
          onDropSuccess();
        }
      });
    }, [successScaleAnim, onDropSuccess]);

    // Store the success animation function in a ref so parent can access it
    const successAnimRef = useRef(triggerSuccessAnimation);
    successAnimRef.current = triggerSuccessAnimation;

    // Expose success animation to parent via a custom property
    React.useEffect(() => {
      if (ref && typeof ref === 'object' && ref.current) {
        (ref.current as any).triggerSuccessAnimation = successAnimRef.current;
      }
    });

    return (
      <View
        ref={ref}
        style={isEndZone ? styles.insertZoneEnd : styles.insertZone}>
        <Animated.View
          style={[
            styles.insertZoneInner,
            isActive && styles.insertZoneActive,
            isEndZone && styles.insertZoneEndInner,
            {
              transform: [
                {scale: Animated.multiply(scaleAnim, successScaleAnim)},
                {scale: pulseAnim},
              ],
              opacity: isEndZone
                ? 0
                : isActive
                ? 0.8
                : isDraggingAnyWord
                ? 0.3
                : 0, // Hide end zone completely, show others when dragging
            },
          ]}>
          {/* Visual indicator for end zone - hidden but keeps detection area */}
        </Animated.View>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  insertZone: {
    width: 2,
    height: 2,
    marginHorizontal: 4,
  },
  insertZoneEnd: {
    width: 20, // Larger width for end zone
    height: 40, // Larger height for end zone
    marginHorizontal: 8,
    marginLeft: 12, // Extra margin to separate from last word
  },
  insertZoneInner: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f8f9fa',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: '#dee2e6',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  insertZoneEndInner: {
    backgroundColor: '#e3f2fd',
    borderWidth: 2,
    borderColor: '#2196f3',
    borderStyle: 'dashed',
  },
  insertZoneActive: {
    backgroundColor: '#e8f5e8',
    borderWidth: 3,
    borderColor: '#4caf50',
    borderStyle: 'solid',
    shadowColor: '#4caf50',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.5,
    shadowRadius: 8,
    elevation: 10,
    transform: [{scale: 1.05}], // Slightly larger when active
  },
});

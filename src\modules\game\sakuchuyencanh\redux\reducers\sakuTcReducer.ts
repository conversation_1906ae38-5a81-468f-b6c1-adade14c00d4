import {createSlice} from '@reduxjs/toolkit';
import {Question} from '../../models/models';
import {
  initData,
  loadGameConfig,
  loadGameQuestions,
} from '../../asyncThunk/sakuTcAsyncThunk';

interface State {
  dataListQuestion: Question[];
  listQuestions: Question[];
  currentQuestion: Question | null;
  currentLevel: number;
  totalQuestion: number;
  questionDone: number;
  bonusLv1: number;
  bonusLv2: number;
  bonusLv3: number;
  timeLimit: number;
  maxLevel: number;
  loading: boolean;
}

const initialState: State = {
  loading: false,
  dataListQuestion: [],
  listQuestions: [],
  currentQuestion: null,
  currentLevel: 0,
  maxLevel: 3,
  totalQuestion: 0,
  questionDone: 0,
  bonusLv1: 0,
  bonusLv2: 0,
  bonusLv3: 0,
  timeLimit: 0,
};

export const SakuTCReducer = createSlice({
  name: 'SakuTCReducer',
  initialState,
  reducers: {
    setData(state, action) {
      state[action.payload.stateName] = action.payload.value;
    },
    startGame: state => {
      state.currentLevel = 1;
      const questionLevel = state.dataListQuestion.filter(
        question => question.level === state.currentLevel,
      );
      state.listQuestions = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.totalQuestion = questionLevel.length;
      state.questionDone = 0;
    },
    nextQuestion: state => {
      state.currentQuestion = state.listQuestions[state.questionDone];
    },
    nextLevel: state => {
      state.currentLevel = state.currentLevel + 1;
      const questionLevel = state.dataListQuestion.filter(
        question => question.level === state.currentLevel,
      );
      state.listQuestions = questionLevel;
      state.currentQuestion = questionLevel[0];
      state.questionDone = 0;
      state.totalQuestion = questionLevel.length;
    },
    restartLevel: state => {
      state.currentQuestion = state.listQuestions[0];
      state.questionDone = 0;
    },
    resetGame: state => {
      state.currentLevel = 1;
      state.currentQuestion = null;
      state.questionDone = 0;
      state.totalQuestion = 0;
      state.listQuestions = [];
    },
  },
  extraReducers: builder => {
    builder.addCase(loadGameConfig.fulfilled, (state, action) => {
      state.bonusLv1 = action.payload.bonusLv1;
      state.bonusLv2 = action.payload.bonusLv2;
      state.bonusLv3 = action.payload.bonusLv3;
      state.timeLimit = action.payload.timeLimit;
    });
    builder.addCase(loadGameQuestions.fulfilled, (state, action) => {
      state.dataListQuestion = action.payload.questions || [];
    });
    builder.addCase(initData.pending, (state, action) => {
      state.loading = true;
    });
    builder.addCase(initData.fulfilled, (state, action) => {
      state.bonusLv1 = action.payload.gameConfig.bonusLv1;
      state.bonusLv2 = action.payload.gameConfig.bonusLv2;
      state.bonusLv3 = action.payload.gameConfig.bonusLv3;
      state.timeLimit = action.payload.gameConfig.timeLimit;
      state.dataListQuestion = action.payload.questions || [];
      state.loading = false;
    });
  },
});

export const {
  setData,
  startGame,
  nextQuestion,
  nextLevel,
  restartLevel,
  resetGame,
} = SakuTCReducer.actions;

export default SakuTCReducer.reducer;

import {useState, useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {
  View,
  FlatList,
  RefreshControl,
  Text,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {navigate, navigateBack, RootScreen} from '../../../../router/router';
import {CourseDA} from '../../../Course/da';
import {SkeletonPlacePostCard} from '../../card/defaultPost';
import {
  FLoading,
  ListTile,
  SkeletonImage,
  TextField,
  Winicon,
} from 'wini-mobile-components';
import TitleWithBackAction from '../../../../Screen/Layout/titleWithBackAction';
import {useRoute} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {myGroupsActions} from '../../reducers/myGroupsReducer';
import {followingGroupsActions} from '../../reducers/followingGroupsReducer';
import ConfigAPI from '../../../../Config/ConfigAPI';

export default function AllGroupsLoadMore() {
  const route = useRoute<any>();
  const {id, title} = route.params;
  const [data, setData] = useState<Array<any>>([]);

  const [isRefresh, setRefresh] = useState(false);
  const [isLoadMore, setLoadMore] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  const {t} = useTranslation();
  const dispatch = useDispatch<any>();
  const followingGroups = useSelector((state: any) => state.followingGroups);
  const myGroups = useSelector((state: any) => state.myGroups);

  var page = 1;
  const size = 20;

  useEffect(() => {
    if (id === 'Following') {
      setData(followingGroups.groups);
    } else if (id === 'Moderration') {
      setData(myGroups.groups);
    }
  }, [id]);

  useEffect(() => {
    onData();
  }, []);

  const onData = async () => {
    if (id === 'Following') {
      dispatch(followingGroupsActions.getFollowingGroups(page, size));
      return;
    }
    if (id === 'Moderration') {
      dispatch(myGroupsActions.getMyGroups(page, size));
      return;
    }
  };

  const onRefresh = async () => {
    setRefresh(true);
    await onData();
    setRefresh(false);
  };

  const handleLoadMore = async () => {
    page++;
    // const result = await courseDA.getAllListbyCategory(
    //   page,
    //   size,
    //   '3aba11bce83144deb9749973b8751142',
    // );
    // if (result.code === 200) {
    //   var lst = data.push(result.data);
    //   setData([lst]);
    //   setLoadMore(false);
    // }
  };

  return (
    <TitleWithBackAction
      onBack={() => {
        navigateBack();
      }}
      bottom={
        <View style={{height: 56, width: '100%'}}>
          <TextField
            style={{paddingHorizontal: 16, width: '100%', height: 40}}
            onChange={async (vl: string) => {
              setSearchValue(vl.trim());
            }}
            value={searchValue}
            placeholder="Tìm kiếm"
            prefix={
              <Winicon
                src="outline/development/zoom"
                size={14}
                color={ColorThemes.light.Neutral_Text_Color_Subtitle}
              />
            }
          />
        </View>
      }>
      <FLoading
        visible={
          id === 'Following' ? followingGroups.isLoading : myGroups.isLoading
        }
        avt={require('../../../../assets/appstore.png')}
      />
      <View
        style={{
          width: '100%',
          flex: 1,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}>
        <FlatList
          data={
            searchValue
              ? data.filter((item: any) =>
                  item.Name.toLowerCase().includes(searchValue.toLowerCase()),
                )
              : data
          }
          refreshControl={
            <RefreshControl
              refreshing={isRefresh}
              onRefresh={() => {
                onRefresh();
              }}
            />
          }
          ListHeaderComponent={() => {
            return (
              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingHorizontal: 16,
                  flexDirection: 'row',
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                }}>
                <Text
                  style={{
                    ...TypoSkin.heading5,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                  }}>
                  Nhóm {title ?? ''}
                </Text>
              </View>
            );
          }}
          contentContainerStyle={{
            gap: 20,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}
          renderItem={({item, index}) => {
            return (
              <ListTile
                key={index}
                onPress={() => {
                  navigate(RootScreen.GroupIndex, {Id: item.Id});
                }}
                style={{padding: 0, paddingHorizontal: 16}}
                title={item.Name}
                titleStyle={{
                  ...TypoSkin.heading7,
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}
                subtitle={
                  <View style={{flex: 1, width: '100%'}}>
                    <Text
                      style={{
                        ...TypoSkin.subtitle3,
                        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}>
                      {item.MemberCount} members
                    </Text>
                  </View>
                }
                leading={
                  <SkeletonImage
                    source={{
                      uri: item.Thumb
                        ? `${ConfigAPI.urlImg + item.Thumb}`
                        : 'https://placehold.co/64/FFFFFF/000000/png',
                    }}
                    height={64}
                    width={64}
                    style={{
                      height: 64,
                      width: 64,
                      borderRadius: 100,
                      backgroundColor:
                        ColorThemes.light.Neutral_Background_Color_Main,
                    }}
                  />
                }
                trailing={
                  <Winicon
                    src="outline/arrows/right-arrow"
                    color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                    size={16}
                  />
                }
              />
            );
          }}
          style={{width: '100%', height: '100%'}}
          keyExtractor={item => item.Id?.toString()}
          horizontal={false}
          onEndReachedThreshold={0.5}
          // onEndReached={handleLoadMore}
          ListFooterComponent={() => {
            return <View style={{height: 16}} />;
          }}
          ListEmptyComponent={() => {
            if (
              id === 'Following'
                ? followingGroups.isLoading
                : myGroups.isLoading
            ) {
              return <SkeletonPlacePostCard />;
            }
            if (isLoadMore) {
              return (
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}>
                  <ActivityIndicator
                    color={ColorThemes.light.Primary_Color_Main}
                  />
                </View>
              );
            }
            return <Text style={{color: '#000000'}}>{t('nodata')}</Text>;
          }}
        />
      </View>
    </TitleWithBackAction>
  );
}

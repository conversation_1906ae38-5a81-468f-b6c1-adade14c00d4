import {createAsyncThunk} from '@reduxjs/toolkit';
import {GameDA} from './gameDA';
import store from '../../redux/store/store';

const gameDa = new GameDA();
const getCurrentScore = createAsyncThunk(
  'Game/getCurrentScore',
  async (gameId: string) => {
    const result = await gameDa.getScoreByCustomerIdAndGameId(
      store.getState().customer.data.Id,
      gameId,
    );
    return result;
  },
);

export {getCurrentScore};

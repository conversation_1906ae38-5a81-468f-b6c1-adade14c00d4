import {createAsyncThunk} from '@reduxjs/toolkit';
import {GetGameConfigRequest} from '../../sakutimban/types/sakuTBTypes';
import {SakutcDa} from '../sakuTcDa';
import {initializeFallbackData} from '../../sakutimban/data/fallbackData';

const sakuTcDa = new SakutcDa();

// Async thunk để load GameConfig từ API
const initData = createAsyncThunk(
  'sakuTcReducer/initData',
  async ({
    gameId,
    stage,
    competenceId,
  }: {
    gameId: string;
    stage: number;
    competenceId: string;
  }) => {
    try {
      const gameConfig = await SakutcDa.getGameConfig(gameId);
      const questions = await sakuTcDa.getQuestionsByGameAndStage(
        gameId,
        stage,
        competenceId,
      );
      return {
        gameConfig,
        questions,
      };
    } catch (error) {
      return {};
    }
  },
);

const loadGameConfig = createAsyncThunk(
  'sakuTcReducer/loadGameConfig',
  async ({gameId}: GetGameConfigRequest) => {
    try {
      const gameConfig = await SakutcDa.getGameConfig(gameId);

      if (gameConfig) {
        return gameConfig;
      }
    } catch (error) {
      return {
        gameId: gameId,
        bonusLv1: 0,
        bonusLv2: 0,
        bonusLv3: 0,
        timeLimit: 0,
      };
    }
  },
);

// Async thunk để load questions từ API
const loadGameQuestions = createAsyncThunk(
  'sakuTcReducer/loadQuestions',
  async ({
    gameId,
    stage,
    competenceId,
  }: {
    gameId: string;
    stage: number;
    competenceId: string;
  }) => {
    try {
      const sakuTcDa = new SakutcDa();
      const questions = await sakuTcDa.getQuestionsByGameAndStage(
        gameId,
        stage,
        competenceId,
      );
      if (questions.length === 0) {
        return {
          questions: [],
        };
      }
      return {
        questions,
      };
    } catch (error) {
      console.error('[Redux] Error loading questions:', error);
      // Fallback to local data
      const fallbackData = initializeFallbackData();
      return {
        questions: [],
      };
    }
  },
);

export {loadGameConfig, loadGameQuestions, initData};

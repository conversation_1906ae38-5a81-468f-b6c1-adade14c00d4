import React from 'react';
import {View, StyleSheet, Text} from 'react-native';
import PDFViewer from './PDFViewer';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';

// Demo component để test PDFViewer với tối ưu hóa
const PDFViewerDemo: React.FC = () => {
  // URL demo PDF (có thể thay đổi thành URL thực tế)
  const demoUrl = '/uploads/sample-document.pdf';

  return (
    <View style={styles.container}>
      <Text style={styles.title}>
        PDF Viewer Demo - Optimized for Large Files
      </Text>
      <Text style={styles.subtitle}>
        Tối ưu hóa cho file PDF lớn trên 10MB với progress tracking và warning
      </Text>
      <PDFViewer
        url={demoUrl}
        fileName="Tài liệu mẫu lớn"
        height={500}
        maxFileSize={10} // Warning for files > 10MB
        enableOptimization={true}
        useGoogleViewer={true} // Use Google Docs Viewer
        onError={error => {
          console.error('Demo PDF error:', error);
        }}
        onLoadStart={() => {
          console.log('Demo PDF loading started');
        }}
        onLoadEnd={() => {
          console.log('Demo PDF loading completed');
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  title: {
    ...TypoSkin.title2,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default PDFViewerDemo;

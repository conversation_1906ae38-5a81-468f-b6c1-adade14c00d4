import React from 'react';
import {useRoute} from '@react-navigation/native';
import {getGameConfig} from '../config/GameConfig';
import GenericGameHomeScreenComponent from '../GenericGameHomeScreen';

// SAKUTB Home Screen using Generic System
const SakuTBHomeScreen: React.FC = () => {
  const route = useRoute<any>();
  const {gameId} = route.params || {gameId: 'SAKUTB'};

  // Get game configuration

  return (
    <GenericGameHomeScreenComponent
      gameId={gameId}
      // Có thể override components nếu cần
      // HeaderComponent={CustomSakuTBHeader}
      // FooterComponent={CustomSakuTBFooter}
      // MilestoneComponent={CustomSakuTBMilestone}
      // StartGameModalComponent={CustomSakuTBModal}
    />
  );
};

export default SakuTBHomeScreen;

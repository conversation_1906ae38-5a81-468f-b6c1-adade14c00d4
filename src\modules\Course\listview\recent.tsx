/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect} from 'react';
import {
  View,
  Text,
  FlatList,
  Pressable,
  ScrollView,
  Dimensions,
} from 'react-native';
import PagerView from 'react-native-pager-view';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {
  DefaultProduct,
  SkeletonPlaceCard,
} from '../../Default/card/defaultProduct';
import {StorageContanst} from '../../../Config/Contanst';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {RootScreen} from '../../../router/router';
import {useNavigation} from '@react-navigation/native';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {useDispatch, useSelector} from 'react-redux';
import {CourseActions} from '../../../redux/reducers/courseReducer';
import {useTranslation} from 'react-i18next';

interface Props {
  titleList?: string;
}

export default function CourseRecent(props: Props) {
  const navigation = useNavigation<any>();
  // const [isLoading, setLoading] = useState(true);
  // const [isLoadMore, setLoadMore] = useState(false);
  // const [data, setData] = useState<Array<any>>([]);
  const {t} = useTranslation();
  const dispatch: AppDispatch = useDispatch();
  const recentData = useSelector((state: RootState) => state.course.recentData);
  const loading = useSelector((state: RootState) => state.course.loading);
  useEffect(() => {
    dispatch(CourseActions.getCourseRecent());
  }, [dispatch]);
  const pages = (array: any[], size: number) => {
    const result = [];
    for (let i = 0; i < array.length; i += size) {
      result.push(array.slice(i, i + size));
    }
    return result;
  };
  return recentData.length > 0 ? (
    <Pressable style={{width: '100%', height: undefined}}>
      {props.titleList ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            paddingBottom: 16,
            flexDirection: 'row',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}>
          <Text
            style={{
              ...TypoSkin.heading5,
              color: ColorThemes.light.Neutral_Text_Color_Title,
            }}>
            {props.titleList}
          </Text>
        </View>
      ) : null}
      <Pressable style={{flex: 1, paddingBottom: 32}}>
        <ScrollView
          style={{height: '100%'}}
          horizontal
          showsHorizontalScrollIndicator={false}>
          {pages(recentData, 5).map((item, index) => (
            <Pressable key={index}>
              <FlatList
                nestedScrollEnabled
                scrollEnabled={false}
                data={item}
                contentContainerStyle={{
                  gap: 16,
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                }}
                renderItem={({item, index}) => {
                  return (
                    <DefaultProduct
                      key={index}
                      flexDirection="row"
                      containerStyle={{
                        paddingHorizontal: 16,
                        width: Dimensions.get('window').width - 32,
                      }}
                      imgStyle={{
                        borderColor:
                          ColorThemes.light.Neutral_Border_Color_Main,
                        borderWidth: 1,
                      }}
                      onPressDetail={() => {
                        navigation.push(RootScreen.CourseDetail, {id: item.Id});
                      }}
                      titleStyle={{
                        ...TypoSkin.heading7,
                        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}
                      onPressLikeAction={async () => {
                        var accessToken = await getDataToAsyncStorage(
                          StorageContanst.accessToken,
                        );
                        if (accessToken) {
                          dispatch(
                            CourseActions.updateWishlistCourse(
                              item.Id,
                              item.IsLike === true,
                            ),
                          );
                        } else {
                          //TODO: add wishlish chưa có token thì navigate tới login và truyền theo router.
                        }
                      }}
                      data={item}
                    />
                  );
                }}
                style={{width: '100%', height: '100%'}}
                keyExtractor={a => a.Id?.toString()}
                onEndReachedThreshold={0.5}
                ListEmptyComponent={() => {
                  if (loading) {
                    return [1, 2, 3].map((_, index) => (
                      <SkeletonPlaceCard key={`skeleton-${index}`} />
                    ));
                  }
                  // if (isLoadMore) {
                  //   return (
                  //     <View
                  //       style={{
                  //         justifyContent: 'center',
                  //         alignItems: 'center',
                  //         flexDirection: 'row',
                  //       }}>
                  //       <ActivityIndicator
                  //         color={ColorThemes.light.Primary_Color_Main}
                  //       />
                  //     </View>
                  //   );
                  // }
                  return <Text style={{color: '#000000'}}>{t('nodata')}</Text>;
                }}
              />
            </Pressable>
          ))}
        </ScrollView>
      </Pressable>
    </Pressable>
  ) : (
    <></>
  );
}

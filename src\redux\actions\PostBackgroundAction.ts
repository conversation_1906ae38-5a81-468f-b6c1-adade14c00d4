import {DataController} from '../../base/baseController';
import {BackgroundData} from '../models/PostBackground';
import {getImage} from './rootAction';

export const postBackgroundAction = {
  fetchAll: async (): Promise<BackgroundData[]> => {
    const controller = new DataController('PostBackgroundM');
    const response = await controller.getAll();

    if (response.code === 200) {
      let data = response.data;
      data = await getImage({items: data});
      return data;
    }
    return [];
  },
};

export async function fetchPostBackground(items: any[]) {
  const postBgIds = items.map(item => item.PostBackgroundMId).filter(id => id); // Filter out null/undefined IDs

  if (postBgIds.length === 0) {
    return items.map(item => ({...item, PostBackgroundM: null})); // Ensure PostBackgroundM is at least null
  }

  const postBgController = new DataController('PostBackgroundM');
  const response = await postBgController.getListSimple({
    query: `@Id:{${postBgIds.join(' | ')}}`,
  });

  if (response.code !== 200) {
    return items.map(item => ({...item, PostBackgroundM: null}));
  }

  const data = await getImage({items: response.data});

  return items.map(item => {
    const postBg = data.find((bg: any) => bg.Id === item.PostBackgroundMId);
    return {...item, PostBackgroundM: postBg || null};
  });
}

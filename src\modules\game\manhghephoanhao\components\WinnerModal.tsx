import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
  Image,
  ImageBackground,
  ActivityIndicator,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import store, {AppDispatch, RootState} from '../../../../redux/store/store';
import {GameStatus, Sakupi, SakupiType} from '../../../../Config/Contanst';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {CustomerActions} from '../../../../redux/reducers/CustomerReducer';
import {Winicon} from 'wini-mobile-components';
import {CustomerDA} from '../../../customer/da';
import {GameDA} from '../../gameDA';
import {randomGID} from '../../../../utils/Utils';
import StaticEnum from '../../../../Config/StaticEnum';
import {
  getBeforeAndAfterRanking,
  RankingInfo,
} from '../../ailatrieuphu/redux/rankingService';
import {gameAction, resetGame} from '../../ailatrieuphu/redux/gameSlice';
import {DataController} from '../../../../base/baseController';

const {width, height} = Dimensions.get('window');
const gameDa = new GameDA();

interface WinnerModalProps {
  visible: boolean;
  onClose: () => void;
  // score: number;
  gameId: string;
  isTimeOut?: boolean;
  competenceId: string;
  restartGame: () => void;
  totalScore: number;
}

const WinnerModal = ({
  visible,
  onClose,
  gameId,
  restartGame,
  competenceId,
  totalScore,
}: // score,
WinnerModalProps) => {
  const navigation = useNavigation<any>();
  const dispatch: AppDispatch = useDispatch();
  const gameState = useSelector((state: RootState) => state.game);
  const sakelcState = useSelector((state: RootState) => state.SakuLC);
  const [score, setScore] = useState(0);

  // State để lưu trữ thông tin xếp hạng
  const [rankingInfo, setRankingInfo] = useState<{
    beforeRanking: RankingInfo | null;
    afterRanking: RankingInfo | null;
  }>({beforeRanking: null, afterRanking: null});

  // State để theo dõi trạng thái tải xếp hạng+
  const [loadingRanking, setLoadingRanking] = useState(false);

  // State để hiển thị thông báo xếp hạng
  const [showRankingMessage, setShowRankingMessage] = useState(false);

  // Function xử lý competence khi hoàn thành 15/15 câu hỏi
  const handleCompetenceCompletion = useCallback(async () => {
    try {
      const gameDa = new GameDA();
      const customerId = store.getState().customer.data.Id;

      if (!customerId) {
        console.error('Không tìm thấy customerId');
        return;
      }

      // Lấy danh sách tất cả competence của game
      const allCompetences = StaticEnum.GameCompetencybyGame.find(
        (comp: any) => comp.gameId === gameId,
      );
      if (!allCompetences || !allCompetences.data) {
        console.error('Không lấy được danh sách competence');
        return;
      }

      // Lấy danh sách competence hiện tại của người chơi
      const customerCompetences =
        await gameDa.getCompetencebyGameIdAndCustomerId(gameId, customerId);

      if (
        customerCompetences &&
        customerCompetences.data &&
        customerCompetences.data.length > 0
      ) {
        // Tìm competence hiện tại (competence đang chơi)
        const currentCompetence = customerCompetences.data.find(
          (comp: any) => comp.Status === 2, // Status = 2 là "process"
        );

        if (currentCompetence) {
          // Update competence hiện tại thành completed
          const updatedCompetence = {
            ...currentCompetence,
            Status: 1, // Status = 1 là "completed"
          };

          await gameDa.editCompetence(updatedCompetence);
          console.log(
            'Đã cập nhật competence hiện tại thành completed:',
            currentCompetence.Name,
          );

          // Tìm competence tiếp theo dựa trên Sort order
          const currentSort = currentCompetence.Sort;
          const nextCompetence = allCompetences.data.find(
            (comp: any) => comp.Sort > currentSort,
          );

          if (nextCompetence) {
            // Kiểm tra xem competence tiếp theo đã tồn tại trong bảng GameCustomerCompetence chưa
            const existingNextCompetence = customerCompetences.data.find(
              (comp: any) => comp.Competency === nextCompetence.id,
            );

            if (!existingNextCompetence) {
              // Add competence tiếp theo với status = process
              const newCompetenceData = {
                Id: randomGID(),
                CustomerId: customerId,
                GameId: gameId,
                Competency: nextCompetence.id,
                Name: nextCompetence.name,
                Sort: nextCompetence.sort,
                Status: 2, // Status = 2 là "process"
                DateCreated: new Date().getTime(),
                IsActive: true,
              };

              await gameDa.addCompetence(newCompetenceData);
              console.log('Đã thêm competence tiếp theo:', nextCompetence.name);
            } else {
              console.log(
                'Competence tiếp theo đã tồn tại:',
                nextCompetence.name,
              );
            }
          } else {
            console.log('Đã hoàn thành tất cả competence!');
          }
        }
      } else {
        // Người chơi chưa có competence nào - add competence đầu tiên với status completed
        console.log(
          'Người chơi chưa có competence nào, thêm competence đầu tiên',
        );

        // Tìm competence đầu tiên (Sort nhỏ nhất)
        const firstCompetence = allCompetences.data.sort(
          (a: any, b: any) => a.Sort - b.Sort,
        )[0];

        if (firstCompetence) {
          // Add competence đầu tiên với status = completed
          const firstCompetenceData = {
            Id: randomGID(),
            CustomerId: customerId,
            GameId: gameId,
            Competency: firstCompetence.id,
            Name: firstCompetence.name,
            Sort: firstCompetence.sort,
            Status: 1, // Status = 1 là "completed"
            DateCreated: new Date().getTime(),
            IsActive: true,
          };

          await gameDa.addCompetence(firstCompetenceData);

          // Tìm competence tiếp theo để add với status = process
          const secondCompetence = allCompetences.data.find(
            (comp: any) => comp.Sort > firstCompetence.sort,
          );

          if (secondCompetence) {
            // Add competence tiếp theo với status = process
            const secondCompetenceData = {
              Id: randomGID(),
              CustomerId: customerId,
              GameId: gameId,
              Competency: secondCompetence.id,
              Name: secondCompetence.name,
              Sort: secondCompetence.sort,
              Status: 2, // Status = 2 là "process"
              DateCreated: new Date().getTime(),
              IsActive: true,
            };

            await gameDa.addCompetence(secondCompetenceData);
          } else {
            console.log('Chỉ có một competence duy nhất trong game!');
          }
        }
      }
    } catch (error) {
      console.error('Lỗi khi xử lý competence:', error);
    }
  }, [gameId]);
  // Lấy thông tin xếp hạng khi modal hiển thị

  const fetchRanking = async () => {
    if (visible) {
      try {
        setLoadingRanking(true);
        // Lấy thông tin xếp hạng trước và sau khi cập nhật điểm
        // Sử dụng điểm từ bảng GameCustomer theo gameId
        const rankingData = await getBeforeAndAfterRanking(totalScore, gameId);

        setRankingInfo(rankingData);

        // Hiển thị thông báo xếp hạng sau 1 giây
        setTimeout(() => {
          setShowRankingMessage(true);
        }, 1000);
      } catch (error) {
        console.error('Lỗi khi lấy thông tin xếp hạng:', error);
      } finally {
        setLoadingRanking(false);
      }
    }
  };
  const fetchScore = async () => {
    if (visible) {
      try {
        // Lấy thông tin điểm từ bảng GameCUstomer
        const gameDa = new GameDA();
        const result = await gameDa.getScoreByCustomerIdAndGameId(
          store.getState().customer.data.Id,
          gameId,
        );
        setScore(result ?? 0);
      } catch (error) {
        console.error('Lỗi khi lấy thông tin điểm:', error);
      }
    }
  };
  // Cập nhật trạng thái milestone khi modal hiển thị
  useEffect(() => {
    if (visible) {
      fetchScore();
      fetchRanking();
      // Lấy thông tin milestone hiện tại
      const currentMilestoneId = gameState.currentMilestoneId;
      if (!currentMilestoneId) {
        return;
      }
      //tính điểm cho người chơi
      gameDa.insertScore({
        milestoneId: currentMilestoneId,
        gameId: gameId,
        competenceId: competenceId,
        status: GameStatus.Completed,
        score: totalScore,
        name: `MANHGHEPHOANHAO_${currentMilestoneId}`,
      });
      // Cập nhật điểm sakupi cho người dùng
      dispatch(CustomerActions.updateRank(SakupiType.game,Sakupi.game, gameId));
      dispatch(CustomerActions.getInfor(false));
      handleCompetenceCompletion();

      dispatch(
        gameAction.updateMilestoneStatus(
          currentMilestoneId,
          GameStatus.Completed,
          totalScore,
          gameId,
        ),
      );
    }
  }, [visible, gameId]);

  const handleNext = () => {
    // Đóng modal trước
    onClose();

    // Sử dụng setTimeout để đảm bảo setState không xảy ra trong quá trình render
    setTimeout(async () => {
      try {
        // Reset game state
        dispatch(resetGame());
        restartGame();
        // Đảm bảo dữ liệu milestone được cập nhật khi quay lại màn hình Home
        // Gọi getMilestones trước khi điều hướng để đảm bảo dữ liệu được cập nhật
        dispatch(gameAction.getMilestones(gameId));

        // Điều hướng đến màn hình Home
        navigation.goBack();
      } catch (error) {
        console.error('Error updating milestones:', error);
        // Vẫn điều hướng về màn hình Home ngay cả khi có lỗi
        navigation.goBack();
      }
    }, 0);
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Tiêu đề Winner */}
          <Text style={styles.winnerText}>Winner</Text>
          {/* Hiển thị điểm số */}
          <ImageBackground
            source={require('../../ailatrieuphu/assets/bg_coin.png')}
            style={styles.scoreContainer}
            resizeMode="contain">
            {/* Điểm cơ bản + điểm thưởng */}
            <View style={styles.scoreItem}>
              <Image
                source={require('../../ailatrieuphu/assets/coin-icon.png')}
                style={styles.diamondIcon}
                // resizeMode="contain"
              />
              <Text style={styles.scoreText}>
                {score} + {totalScore}
              </Text>
            </View>

            {/* Tổng điểm */}
            <View style={styles.scoreItem}>
              <Image
                source={require('../../ailatrieuphu/assets/rank.png')}
                style={styles.diamondIcon}
              />
              {loadingRanking ? (
                <ActivityIndicator size="small" color="#FFD700" />
              ) : (
                <>
                  <Text style={styles.scoreText}>
                    {rankingInfo?.afterRanking?.rank}
                  </Text>
                  {rankingInfo?.afterRanking?.previousRank &&
                    rankingInfo?.afterRanking?.rank <
                      rankingInfo?.afterRanking?.previousRank && (
                      <Text style={styles.rankUpText}>
                        <Winicon
                          src={'color/arrows/triangle-up'}
                          size={9}
                          color="#4CAF50"
                        />{' '}
                        {rankingInfo?.afterRanking?.previousRank -
                          rankingInfo?.afterRanking?.rank}
                      </Text>
                    )}
                </>
              )}
            </View>
          </ImageBackground>

          {/* Hình ảnh chim vui mừng */}
          <View style={styles.birdContainer}>
            {/* <View style={styles.birdImage} /> */}
            <Image
              source={require('../../ailatrieuphu/assets/result_icon.png')}
              style={styles.birdImage}
            />
          </View>

          {/* Nút Next */}
          <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
            <Image
              source={require('../../ailatrieuphu/assets/next_btn.png')}
              style={styles.nextButton}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.69)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: width * 0.93,
    height: height * 0.76,
    backgroundColor: 'rgba(112, 90, 64, 0.96)', // Màu nền nâu như trong ảnh
    padding: 20,
    marginBottom: 20,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  winnerText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#FFD700', // Màu vàng
    textAlign: 'center',
    marginTop: 20,
    textShadowColor: '#000',
    textShadowOffset: {width: 2, height: 2},
    textShadowRadius: 5,
    fontFamily: 'BagelFatOne-Regular',
  },
  scoreContainer: {
    alignItems: 'center',
    marginTop: 20,
    width: '100%',
  },
  scoreItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 20,
    height: 30,
    justifyContent: 'center',
    position: 'relative',
    marginBottom: 20,
    paddingHorizontal: 10,
    paddingLeft: 20,
  },
  diamondIcon: {
    marginRight: 10,
    borderRadius: 15,
    width: 40,
    height: 47,
    position: 'absolute',
    left: -20,
    top: -10,
  },
  trophyIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
    backgroundColor: '#FFD700',
    borderRadius: 15,
  },
  scoreText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 10,
  },
  totalScoreText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  birdContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  birdImage: {
    resizeMode: 'contain',
  },
  congratsText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginTop: 10,
  },
  nextButton: {
    width: 230,
    height: 90,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  rankingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
    minHeight: 50,
  },
  rankingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 5,
  },
  rankUpText: {
    fontSize: 12,
    marginLeft: 10,
    fontWeight: 'bold',
    color: '#4CAF50', // Màu xanh lá cây
    textAlign: 'center',
  },
});

export default WinnerModal;
